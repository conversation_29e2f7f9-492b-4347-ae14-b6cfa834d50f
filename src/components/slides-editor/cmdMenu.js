
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Popover, TextareaAutosize } from "@mui/material";
import './cmdMenu.css'
import { useDispatch, useSelector } from 'react-redux';
// import { useIntl } from 'react-intl';
import { Magic } from '@styled-icons/bootstrap/Magic'
import { Wand } from '@styled-icons/fluentui-system-filled/Wand'
import { H1 } from '@styled-icons/remix-editor/H1'
import { H2 } from '@styled-icons/remix-editor/H2'
import { H3 } from '@styled-icons/remix-editor/H3'
import { FormatQuote } from '@styled-icons/material/FormatQuote'
import { Link } from '@styled-icons/material/Link'
import { Image } from '@styled-icons/material-outlined/Image'
import { MathFormatProfessional } from '@styled-icons/fluentui-system-regular/MathFormatProfessional'
import { Code } from '@styled-icons/bootstrap/Code'
import { Text } from '@styled-icons/remix-editor/Text'
import { Table } from '@styled-icons/bootstrap/Table';
import { Slash } from '@styled-icons/bootstrap/Slash'
import { InsertColumnRight } from '@styled-icons/remix-editor/InsertColumnRight'
import { InsertRowBottom } from '@styled-icons/remix-editor/InsertRowBottom'
import { SpeakerNotes } from '@styled-icons/material-outlined/SpeakerNotes'
import { Superscript } from '@styled-icons/material/Superscript'
import { Subscript } from '@styled-icons/material/Subscript'
import { BarChartLine } from '@styled-icons/bootstrap/BarChartLine'
import { Poll } from '@styled-icons/material-outlined/Poll'
import { undo } from '@codemirror/commands';
import { useIntl } from 'react-intl';
import { AI_ASSISTANT_DIALOG, CHART_GENERATOR_DIALOG, IMAGE_UPLOAD_DIALOG, LINK_INPUT_DIALOG, POLL_GENERATOR_DIALOG, TABLE_BUILDER_DIALOG } from 'src/constants/actionTypes';
import { FormatListBulleted, FormatListNumbered } from '@styled-icons/material';
import { getTargetSlide } from 'src/utils/SlidesUtil';
import { Icons } from '@/components/icons';

const TEXT_LEN_LIMIT = 2000;

export const CmdMenu = ({ state, hid, editorView, onClose, insert }) => {
    const intl = useIntl();
    const { isVisible, position, lineInfo, lineElement } = state;
    // const [style, setStyle] = useState({ display: 'none', width: 300, maxHeight: 400 });
    const dispatch = useDispatch();
    const ref = useRef();
    const shouldUseDarkColors = useSelector(state => state.uiState.should_use_dark_colors);

    const [itemSelected, setItemSelected] = useState(0);
    const [filteredItems, setFilteredItems] = useState([]);

    useEffect(() => {
        if (!lineInfo?.searchText) {
            setFilteredItems(items)
        } else {
            setFilteredItems(items.filter(item => {
                let label = intl.formatMessage({ id: `cmd_${item.id}` });
                let desc = intl.formatMessage({ id: `cmd_${item.id}_desc` });
                let searchText = lineInfo.searchText.trim().toLowerCase();
                return item.id === 'nothing' || item.id.includes(searchText) || label.toLowerCase().includes(searchText) || desc.toLowerCase().includes(searchText);
            }));
        }

        setItemSelected(0);
    }, [lineInfo?.searchText])

    const keydownHandler = (event) => {
        if (event.key === 'Escape') {
            onClose && onClose();
            event.preventDefault();
            event.stopPropagation();
            return true
        }

        if (['Enter', 'ArrowUp', 'ArrowDown'].includes(event.key)) {
            if (event.key === 'ArrowUp') {
                setItemSelected(itemSelected === 0 ? items.length - 1 : itemSelected - 1);
            } else if (event.key === 'ArrowDown') {
                setItemSelected(itemSelected === items.length - 1 ? 0 : itemSelected + 1);
            }

            // 使用 Enter 键触发操作
            if (event.key === 'Enter') {
                const selectedItem = filteredItems[itemSelected];
                undo(editorView);

                if(selectedItem) {
                    performAction(selectedItem);
                } else {
                    onClose()
                }
            }
            event.preventDefault();
            event.stopPropagation();
            return true
        }
    }

    const performAction = (item) => {
        if (item.id != 'trigger') {
            removeSlashCmd();
        }

        if (item.action && typeof item.action === 'function') {
            item.action();
        }

        onClose && onClose();
    }

    useEffect(() => {
        if (isVisible && editorView?.contentDOM) {
            editorView.contentDOM.addEventListener('keydown', keydownHandler);
        }

        return () => {
            editorView?.contentDOM?.removeEventListener('keydown', keydownHandler);
        }
    }, [isVisible, itemSelected, filteredItems, editorView?.contentDOM])

    const removeSlashCmd = useCallback(() => {
        editorView.dispatch({
            changes: { from: lineInfo.searchStart + lineInfo.from - 1, to: lineInfo.searchEnd + lineInfo.from, insert: '' },
        })
    }, [editorView, lineInfo]);

    const elmRefs = React.useRef({});
    React.useLayoutEffect(() => {
        if (!elmRefs) return;

        elmRefs[itemSelected] && elmRefs[itemSelected].current.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
        });

        const selectedItem = elmRefs.current[itemSelected];
        selectedItem && selectedItem.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
        });

        // let item = groupedAssistantItems.find(it => it.order == itemTargeted);
        // set_sub_menu_items(item?.sub_items);

    }, [itemSelected]);

    const selectContent = () => {
        if (!editorView) return {}
        const focus = editorView.state.selection?.ranges[0]?.from;

        let targetSlide = getTargetSlide(editorView.state.doc.toString(), editorView.state.doc.lineAt(focus).number)


        editorView.dispatch({
            selection: { anchor: targetSlide.currentPageToCurrentLine.start, head: targetSlide.currentPageToCurrentLine.end }
        })

        return targetSlide.currentPageToCurrentLine.content
    }

    const iconColor = shouldUseDarkColors ? '#777' : '#999';
    const items = [{
        id: 'trigger',
        icon: <Text color={iconColor} style={iconStyle} />,
    }, {
        id: 'ai_continue',
        icon: <Wand color={'dodgerblue'} style={iconStyle} />,
        action: () => {
            dispatch({
                type: AI_ASSISTANT_DIALOG,
                value: {
                    caller: 'slides', visible: true, trigger: 'cmd', action: 'continue',
                    objType: 'slides',
                    anchorEl: lineElement,
                    selectedText: selectContent()
                }
            });
        }
    }, {
        id: 'ai_assistant',
        icon: <Magic color={'dodgerblue'} style={iconStyle} />,
        action: () => {
            dispatch({
                type: AI_ASSISTANT_DIALOG,
                value: {
                    caller: 'slides',
                    objType: 'slides',
                    visible: true, trigger: 'cmd',
                    anchorEl: lineElement,
                    selectedText: selectContent(),
                    pageContent: editorView.state.doc.toString()
                }
            });
        }
    }, {
        id: 'h1',
        icon: <Icons.h1 color={iconColor} style={iconStyle} />,
        action: () => insert(lineInfo.from, '# ')
    }, {
        id: 'h2',
        icon: <Icons.h2 color={iconColor} style={iconStyle} />,
        action: () => insert(lineInfo.from, '## ')
    }, {
        id: 'h3',
        icon: <Icons.h3 color={iconColor} style={iconStyle} />,
        action: () => insert(lineInfo.from, '### ')
    }, {
        id: 'bulleted_list',
        icon: <Icons.ul color={iconColor} style={iconStyle} />,
        action: () => insert(lineInfo.from, `- `)
    }, {
        id: 'ordered_list',
        icon: <Icons.ol color={iconColor} style={iconStyle} />,
        action: () => insert(lineInfo.from, `1. `)
    }, {
        id: 'table',
        icon: <Icons.table color={iconColor} style={iconStyle} />,
        action: () => dispatch({ type: TABLE_BUILDER_DIALOG, value: { visible: true, data: { columns: 3, rows: 2 } } })
    }, {
        id: 'blockquote',
        icon: <Icons.blockquote color={iconColor} style={{ ...iconStyle, width: 23, height: 23 }} />,
        action: () => insert(lineInfo.from, '> ')
    }, {
        id: 'image',
        icon: <Icons.image color={iconColor}  style={iconStyle}/>,
        action: () => {
            dispatch({ type: IMAGE_UPLOAD_DIALOG, value: { visible: true, trigger: 'slides_editor', hid } });
        }
    }, {
        id: 'link',
        icon: <Icons.link color={iconColor} style={iconStyle} />,
        action: () => dispatch({
            type: LINK_INPUT_DIALOG,
            value: {
                visible: true,
                textEnabled: true,
                trigger: 'link'
            }
        })
    }, {
        id: 'hslide',
        icon: <InsertColumnRight color={iconColor} style={iconStyle} />,
        action: () => insert(-1, `\n---\n`, 0)
    }, {
        id: 'vslide',
        icon: <InsertRowBottom color={iconColor} style={iconStyle} />,
        action: () => insert(-1, `\n--\n`, 0)
    }, {
        id: 'notes',
        icon: <SpeakerNotes color={iconColor} style={iconStyle} />,
        action: () => insert(-1, `\nNotes: `, 0)
    }, {
        id: 'mathblock',
        icon: <MathFormatProfessional color={iconColor} style={iconStyle} />,
        action: () => insert(-1, `\`$$\n\\begin{aligned}\n\n\\end{aligned}\n$$\``, 18)
    }, {
        id: 'codeblock',
        // icon: <div style={{ ...iconStyle, fontSize: '16px', color: iconColor }}>CB</div>,
        icon: <Icons.codeblock color={iconColor} style={iconStyle} />,
        action: () => insert(-1, '\`\`\`\n\n\`\`\`', 4)
    }, {
        id: 'codeline',
        icon: <Icons.code color={iconColor} style={iconStyle} />,
        action: () => insert(-1, '\`\`', 1)
    }, {
        id: 'iframe',
        icon: <div style={{ ...iconStyle, fontSize: '18px', color: iconColor }}>P</div>,
        action: () => dispatch({
            type: LINK_INPUT_DIALOG,
            value: {
                visible: true,
                textEnabled: false,
                trigger: 'iframe'
            }
        })
    }, {
        id: 'chart',
        icon: <BarChartLine color={iconColor} style={{ ...iconStyle, width: 24, height: 24 }} />,
        action: () => dispatch({
            type: CHART_GENERATOR_DIALOG,
            value: {
                visible: true
            }
        })
    }, {
    //     id: 'poll',
    //     icon: <Poll color={iconColor} style={iconStyle} />,
    //     action: () => dispatch({
    //         type: POLL_GENERATOR_DIALOG,
    //         value: {
    //             visible: true
    //         }
    //     })
    // }, {
        id: 'superscript',
        icon: <Icons.superscript color={iconColor} style={iconStyle} />,
        action: () => insert(-1, `<sup></sup>`, 6)
    }, {
        id: 'subscript',
        icon: <Icons.subscript color={iconColor} style={iconStyle} />,
        action: () => insert(-1, `<sub></sub>`, 6)
    }, {
        id: 'slidecolor',
        icon: <div style={{ ...iconStyle, fontSize: '18px', color: 'hotpink' }}>C</div>,
        action: () => insert(-1, `<!--.slide: color=""-->\n`, 5)
    }
    // , {
    //     id: 'fullscreen',
    //     icon: <div style={{ ...iconStyle, fontSize: '20px', color: iconColor }}>f</div>,
    //     action: () => insert(-1, `<!--.element: class="fullscreen"-->`, 0)
    // }, {
    //     id: 'fullscreencover',
    //     icon: <div style={{ ...iconStyle, fontSize: '18px', color: iconColor }}>F</div>,
    //     action: () => insert(-1, `<!--.element: class="fullscreen cover"-->`, 0)
    // }, {
    //     id: 'background',
    //     icon: <div style={{ ...iconStyle, fontSize: '16px', color: iconColor }}>BG</div>,
    //     action: () => insert(-1, `<!--.element: class="slides-background cover"-->`, 0)
    // }
    ];

    // console.log('position............', position, isVisible, filteredItems)

    if (!position || !isVisible) {
        return <></>
    }

    return <div
        className={`popup_wrapper_${shouldUseDarkColors ? 'dark' : 'light'}`}
        style={{ ...position, position: 'absolute', width: 300, maxHeight: 300, overflowY: 'scroll' }}
        ref={ref}
    >
        {
            filteredItems?.map((item, index) => {
                return <div
                    className={`hoverStand_${shouldUseDarkColors ? 'dark' : 'light'}`}
                    key={index + ''}
                    style={{
                        backgroundColor: itemSelected === index ? `${shouldUseDarkColors ? '#6699ff21' : '#cceeff66'}` : undefined,
                        padding: '8px'
                    }}
                    ref={(ref) => {
                        elmRefs.current = { ...elmRefs.current, [index]: ref };
                    }}
                    onClick={() => performAction(item)}
                >
                    <div className={`icon_${shouldUseDarkColors ? 'dark' : 'light'}`} style={{ borderRadius: '4px', padding: '4px', marginRight: '8px' }}>{item.icon}</div>
                    <div>
                        <div>{intl.formatMessage({ id: `cmd_${item.id}` })}</div>
                        <div style={{ fontSize: '12px', color: 'gray' }}>{intl.formatMessage({ id: `cmd_${item.id}_desc` })}</div>
                    </div>
                </div>
            })
        }
    </div>
}

const iconStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '26px',
    height: '26px'
}