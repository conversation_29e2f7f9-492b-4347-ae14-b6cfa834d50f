'use client';

import './code-block-element.css';

import React, { forwardRef, useState } from 'react';
import {
  TCodeBlockElement,
  useCodeBlockElementState,
} from '@udecode/plate-code-block';
import { PlateElement, PlateElementProps, Value, getNodeString, getNodeTexts } from '@udecode/plate-common';

import { cn } from '@/lib/utils';

import { CodeBlockCombobox } from './code-block-combobox';
import { ClipboardCode } from '@styled-icons/fluentui-system-regular/ClipboardCode';
import { ClipboardTask } from '@styled-icons/fluentui-system-regular/ClipboardTask'
import { Display } from '@styled-icons/bootstrap/Display'
import { Tooltip } from '@mui/material';
import { useIntl } from 'react-intl';
import MermaidRenderer from '../common/MermaidRenderer';
import Artifact from '../flow/Artifact';

const CodeBlockElement = forwardRef<
  HTMLDivElement,
  PlateElementProps<Value, TCodeBlockElement>
>(({ className, ...props }, ref) => {
  const { children, element } = props;

  const state = useCodeBlockElementState({ element });
  const [copied, setCopied] = useState(false);
  const intl = useIntl();
  const [editMode, setEditMode] = useState(false);

  return (
    <PlateElement
      ref={ref}
      className={cn('relative py-1', state.className, className)}
      {...props}
    >
      {
        ['mermaid', 'svg'].includes(element.lang || '') && !editMode &&
        <Artifact
          inDoc={true}
          selectedArtifact={{
            type: element.lang === 'mermaid' && 'Mermaid' || element.lang === 'svg' && 'SVG',
            content: Array.from(getNodeTexts(element)).map(line => line[0].text).join('\n'),
            id: element.id
          }}

          setSelectedArtifact={null}
          aigc_hovered={undefined}
          color_theme={null}
          offsetTop={undefined}
          onEdit={() => {
            setEditMode(true)
          }}
        />
      }

      <pre
        style={{
          display: (!['mermaid', 'svg'].includes(element.lang || '') || editMode) ? undefined : 'none'
        }}
        className="overflow-x-auto rounded-md bg-muted px-6 py-8 font-mono text-sm leading-[normal] [tab-size:2]">
        <code>{children}</code>
      </pre>

      {
        (!['mermaid', 'svg'].includes(element.lang || '') || editMode) &&
        state.syntax && (
          <div
            className="absolute right-2 top-2 z-10 select-none"
            style={{ display: 'flex', flexDirection: 'row' }}
            contentEditable={false}
          >
            {
              ['mermaid', 'svg'].includes(element.lang || '') &&
              <Tooltip title={intl.formatMessage({ id: 'display' })} placement="top">
                <div
                  className='hoverStand'
                  style={{ width: 40, padding: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                  onClick={() => setEditMode(false)}
                >
                  <Display size={16} />
                </div>
              </Tooltip>
            }
            <Tooltip title={intl.formatMessage({ id: copied ? 'copied' : 'copy' })} placement="top">
              <div
                className='hoverStand'
                style={{ width: 40, padding: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                onClick={() => navigator.clipboard.writeText(Array.from(getNodeTexts(element)).map(line => line[0].text).join('\n')).then(() => {
                  setCopied(true);
                  setTimeout(() => setCopied(false), 3000)
                })}
              >
                {
                  !copied &&
                  <ClipboardCode size={16} />
                }
                {
                  copied &&
                  <ClipboardTask size={16} color='green' />
                }
              </div>
            </Tooltip>
            <CodeBlockCombobox />
          </div>
        )}
    </PlateElement>
  );
});
CodeBlockElement.displayName = 'CodeBlockElement';

export { CodeBlockElement };
