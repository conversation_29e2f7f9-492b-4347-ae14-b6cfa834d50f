import { But<PERSON>, Di<PERSON><PERSON>, <PERSON>I<PERSON>, <PERSON>ItemText, Popover, Tooltip } from '@mui/material';
import * as React from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';
import { callAIAssist, callAnthropic, callOpenAI, callPaLM, fetchPrompts, fetchPromptsPinned, fetchPromptsWorkspace, getDoc, refreshAIResponse, upsertDoc } from 'src/actions/ticketAction';
import { AI_ASSISTANT_DIALOG, CONFIRM_DIALOG, DOC_ACTIONS, INVITE_FRIENDS_DIALOG, PRIVATE_DOCS_ACTIONS, SETTINGS_DIALOG, TEXT_SELECTION, TITLE_SELECTION } from 'src/constants/actionTypes';
import { Magic } from '@styled-icons/bootstrap/Magic'
import { Send } from '@styled-icons/bootstrap/Send';
import { useIntl } from 'react-intl';
import { getNode, selectEditor, toDOMNode } from '@udecode/plate-common';
import { slateToMD, trimMarkdown } from '../../utils/SlateMarkdown';
import 'katex/dist/katex.min.css'
import CircularProgress from '@mui/material/CircularProgress';
import { Warning } from '@styled-icons/fluentui-system-regular/Warning'
import { CheckboxUnchecked } from '@styled-icons/fluentui-system-regular/CheckboxUnchecked'
import { CheckboxChecked } from '@styled-icons/fluentui-system-regular/CheckboxChecked'

import { Check, KeyboardReturn, Refresh } from '@styled-icons/material';
import { Selector } from '../common/Selector';
import { getStateByUser } from 'src/reducers/listReducer';
import { cloneDeep } from 'lodash';
import { PageChooserMenu } from '../PageChooserMenu';
import { linkToPage } from '@/utils/PageLinkMaker';
import { useHistory, useLocation } from 'react-router-dom';
import { ELEMENT_DB_EMBED } from '@/plate-plugins/db-embeded';
import { ELEMENT_IMAGE } from '@udecode/plate-media';
import { ELEMENT_SLIDES_EMBED } from '@/plate-plugins/slides-embeded';
import { ELEMENT_SUB_PAGE } from '@/plate-plugins/sub-page-link/createSubPageLinkPlugin';
import ConfirmMessage from './confirm_message';
import { geminiStreamGenerate, openAIStreamGenerate } from '@/utils/serverAPIUtil';
import ContentEditable from 'react-contenteditable';
import TurndownService from 'turndown';
import MarkdownRenderer from '../common/MarkdownRenderer';
import ModelSelector from '../flow/ModelSelector';
import { LLM_API_KEY_MODAL } from '../../constants/actionTypes';
import { extractJSONFromString } from '../../utils/jsonStringUtil';
import { Copy } from '@styled-icons/fluentui-system-regular';
import { ArrowToBottom } from '@styled-icons/boxicons-regular/ArrowToBottom';
import { ArrowSwap } from '@styled-icons/fluentui-system-regular';
import { Wand } from '@styled-icons/fluentui-system-filled/Wand'
import { Translate } from '@styled-icons/bootstrap/Translate'
import { ArrowBack } from '@styled-icons/boxicons-regular/ArrowBack'
import { Close } from '@styled-icons/material/Close'
import { Expand } from '@styled-icons/material/Expand'
import { SearchGroundings } from '../common/SearchGroundings'

const TEXT_LEN_LIMIT = 4000;

const AI_API_CALLER = {
  openai: callOpenAI,
  groq: callOpenAI,
  'openai_compatible': callOpenAI,
  anthropic: callAnthropic,
  gemini: callPaLM
}

const removeQuotes = (str) => {
  if (!str) {
    return "";
  }

  return str.replace(/^````/, "").replace(/````$/, "");
}

const AIModal = ({ }) => {
  const dialogState = useSelector(state => state.uiState.aiDialog) || {};
  const loginUser = useSelector(state => state.loginIn && state.loginIn.user);
  const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
  const workingSpace = orgs.items.find(org => org._id === loginUser.workingOrgId);
  const prompts = useSelector(state => state.prompts);
  const prompt_lists = useSelector(state => getStateByUser(state.prompt_lists, loginUser));
  const pinned_prompt_lists = useSelector(state => getStateByUser(state.pinned_prompt_lists, loginUser));
  const workspace_prompt_lists = useSelector(state => getStateByUser(state.workspace_prompt_lists, loginUser));
  const public_prompt_lists = useSelector(state => getStateByUser(state.public_prompt_lists, loginUser));
  const ai_use_search = useSelector(state => state.uiState.ai_use_search);

  const currentLocation = useLocation();
  const params = new Proxy(new URLSearchParams(currentLocation.search), {
    get: (searchParams, prop) => searchParams.get(prop) || '',
  });
  const hid = params.hid;

  const editor = useSelector(state => state.uiState.currentEditor);
  const docs = useSelector(state => state.docs);
  const [anchorEl, setAnchorEl] = React.useState();
  const [sub_menu_anchor, set_sub_menu_anchor] = React.useState();
  const dispatch = useDispatch();
  const intl = useIntl();
  const textInputRef = React.useRef();
  const selectedItemRef = React.useRef(null);
  const contentContainerRef = React.useRef(null);
  const history = useHistory();

  const app_config = useSelector(state => state.uiState.app_config);
  const { assistant_items, assistant_items_groups } = app_config || {};
  const [ai_items, set_ai_items] = React.useState([]);

  const [searchText, setSearchText] = React.useState('');
  const [userInput, setUserInput] = React.useState();
  const [itemTargeted, setItemTargeted] = React.useState(0);
  const [sub_menu_parent, set_sub_menu_parent] = React.useState(-1);
  const [sub_menu_items, set_sub_menu_items] = React.useState([]);
  const [sub_menu_item_targeted, set_sub_menu_item_targeted] = React.useState(0);
  const [sub_menu_visible, set_sub_menu_visible] = React.useState(false);
  const [avaliableAssistantItems, setAvaliableAssistantItems] = React.useState(assistant_items);
  const [extendedAssistantItems, setExtendedAssistantItems] = React.useState([]);
  const [filteredAssistantItems, setFilteredAssistantItems] = React.useState([]);
  const [groupedAssistantItems, setGroupedAssistantItems] = React.useState([]);
  const [confirmMessage, setConfirmMessage] = React.useState();
  const [drafter, setDrafter] = React.useState([]);
  const [selectedText, setSelectedText] = React.useState();
  const [attach_selected_content, set_attach_selected_content] = React.useState();

  const [context, setContext] = React.useState();
  const usingTitle = useSelector(state => state.uiState.titleSelected);
  const [errMsg, setErrMsg] = React.useState();
  const [contextErrMsg, setContextErrMsg] = React.useState();
  const [entranceWithAI, setEntranceWithAI] = React.useState(false);
  const form_input_ref = React.useRef();
  const [isInputZh, setIsInputZh] = React.useState();

  const llm_api_keys = useSelector(state => state.uiState.llm_api_keys);
  const ai_api_model = useSelector(state => state.uiState.ai_api_model);
  const [temp_llm_model, set_temp_llm_model] = React.useState();

  const using_model = React.useMemo(() => {
    const model_id = temp_llm_model || ai_api_model;
    let model = llm_api_keys?.find(item => item.id === model_id) || app_config?.ai_api_models?.find(item => item.value === model_id);

    return model;
  }, [app_config?.ai_api_models, llm_api_keys, ai_api_model, temp_llm_model])

  const apiKeyDialogState = useSelector(state => state.uiState.llmApiKeyDialog);
  React.useEffect(() => {
    if (!apiKeyDialogState?.visible && apiKeyDialogState?.confirmedModelId) {

      llm_api_keys?.find(item => item.id === apiKeyDialogState.confirmedModelId) && set_temp_llm_model(apiKeyDialogState.confirmedModelId);

      dispatch({
        type: LLM_API_KEY_MODAL,
        value: {
          visible: false
        }
      })
    }
  }, [apiKeyDialogState])

  React.useEffect(() => {
    set_temp_llm_model(null);
  }, [hid])

  React.useEffect(() => {
    dispatch(fetchPrompts({}));
    dispatch(fetchPromptsPinned({}));
  }, []);

  React.useEffect(() => {
    if (!dialogState?.visible || workingSpace?.users?.length < 2) return;

    dispatch(fetchPromptsWorkspace({}))
  }, [dialogState?.visible, workingSpace])

  React.useEffect(() => {
    if (!userInput) {
      return setSearchText('');
    }

    const turndownService = new TurndownService();
    const mrkd = turndownService.turndown(userInput)?.trim();

    setSearchText(mrkd)
  }, [userInput])

  const isDesiredModal = React.useCallback((ai_item, ai_sub_item, model) => {
    if (ai_item?.llms && !ai_item.llms.find(item => item.startsWith(model?.value || model?.model)) || ai_sub_item?.llms && !ai_sub_item.llms.find(item => item.startsWith(model?.value || model?.model))) {
      setConfirmMessage({
        onConfirm: () => {
          set_temp_llm_model((ai_item?.llms || ai_sub_item?.llms)[0]);
          setConfirmMessage(null)
        },
        content: intl.formatMessage({ id: 'action_requires_specific_models' }, { models: (ai_item?.llms || ai_sub_item?.llms)?.join(', '), switch_to_model: (ai_item?.llms || ai_sub_item?.llms)[0] })
      })

      return false;
    }

    return true;
  }, []);

  const prompt_to_ai_item = React.useCallback((prompt) => {
    return {
      label: prompt.name,
      type: 'user_installed',
      action: prompt._id,
      prompt_user: prompt.prompt + (prompt.content_source == 'selected' ? '.\nGiven text: {{selected_text}}' : ''),
      args: prompt.args || [],
      objTypes: ['markdown', 'slides']
    }
  }, []);

  const get_ai_prompt_item = React.useCallback((action) => {
    let item = assistant_items.find(it => it.action == action);
    if (item) return item;

    item = prompt_lists?.items?.find(it => it._id == action) ||
      pinned_prompt_lists?.items?.find(it => it._id == action) ||
      public_prompt_lists?.items?.find(it => it._id == action) ||
      prompts?.byId[action];

    if (!item) return;

    return prompt_to_ai_item(item);
  }, [assistant_items, prompt_lists, pinned_prompt_lists, public_prompt_lists, prompts])

  React.useEffect(() => {
    let items = [];

    assistant_items?.forEach(item => {
      let index = items.findIndex(it => it.action === item._id);
      if (index == -1) {
        if (item.group === 5) {
          //for slides group
          item.prompt = item.prompt + '.\nGiven text: {{selected_text}}';
        }
        items.push(item);
      }
    })

    let my_prompts = prompt_lists?.items
    if (my_prompts?.length > 0) {
      items = items.filter(item => item.action !== 'add_prompt');
      my_prompts.forEach(item => {
        let assist_item = prompt_to_ai_item(item);
        assist_item.group = 9;

        let index = items.findIndex(it => it.action === item._id);
        if (index > -1) {
          items[index] = assist_item;
        } else {
          items.push(assist_item);
        }
      })
    }

    if (pinned_prompt_lists?.items?.length > 0) {
      pinned_prompt_lists.items.forEach(item => {
        let assist_item = prompt_to_ai_item(item);
        assist_item.group = 8;

        let index = items.findIndex(it => it.action === item._id);
        if (index > -1) {
          items[index] = assist_item;
        } else {
          items.push(assist_item);
        }
      })
    }

    if (workspace_prompt_lists?.items?.length > 0) {
      workspace_prompt_lists.items.forEach(item => {
        let assist_item = prompt_to_ai_item(item);
        assist_item.group = 7;

        let index = items.findIndex(it => it.action === item._id);
        if (index > -1) {
          items[index] = assist_item;
        } else {
          items.push(assist_item);
        }
      })
    }

    set_ai_items(items);
    setDrafter(items.find(item => item.action == 'draft'));
  }, [prompt_lists, pinned_prompt_lists, workspace_prompt_lists, assistant_items])

  const setTextSelection = (selection) => {
    dispatch({
      type: TEXT_SELECTION,
      value: selection
    })
  }

  const setTitleSelected = (selected) => {
    dispatch({
      type: TITLE_SELECTION,
      value: selected
    })
  }

  const extractContextFromBlocks = React.useCallback((blocks) => {
    if (!blocks) return null;

    //filter some blocks, such as img...
    blocks = blocks.filter(block => ![ELEMENT_DB_EMBED, ELEMENT_IMAGE, ELEMENT_SLIDES_EMBED, ELEMENT_SUB_PAGE].includes(block.type));

    return trimMarkdown(slateToMD(blocks), 3000);
  }, [])

  const getSelectedBlocksForCmd = (focus) => {
    let start, end;
    let children = [];
    if (focus) {
      end = focus.path[0];
    } else {
      end = editor.children.length - 1;
    }

    for (let i = end; i >= 0; i--) {
      start = i;
      children.unshift(editor.children[i]);

      if (slateToMD(children).length > TEXT_LEN_LIMIT) {
        if (start != end) {
          children.shift();
          start++;
        }
        return { range: { start, end }, children }
      }
    }

    let usingTitle;
    if (start === 0 && ((docs.byId[dialogState.hid]?.title || '') + '\n\n' + slateToMD(children)).length < TEXT_LEN_LIMIT) {
      usingTitle = true;
    } else {
      usingTitle = false;
    }

    return { range: { start, end }, children, usingTitle }
  }

  const [width, setWidth] = React.useState('820px');

  const filterDefaultTitle = React.useCallback((title) => {
    if (['Untitled Page', '新页面', '无标题页面', 'New Page', '新幻灯片', 'New Slides'].includes(title)) {
      return ''
    }

    return title;
  }, []);

  React.useEffect(() => {
    if (!dialogState.visible) {
      setSelectedText('');

      if (loading) {
        setCancelled(doing);
      }


      setDoing(null);
      setEntranceWithAI(false);

      setCopied(false);
      setForm(null);
      setContext(null);
      setConfirmMessage(null);

      return;
    }

    if (dialogState.caller === 'plate' && !editor) return;

    setTitleSelected(false);
    setAiResponse(null);
    setPrevAIResponse(null);
    setUserInput(dialogState.query || '');
    setLoading(false);
    setErrMsg('');
    setContextErrMsg(null);
    setCopied(false);
    setForm(null);
    setContext(null);
    setCancelled(null);
    setConfirmMessage(null);

    if (dialogState.caller === 'slides') {
      setAnchorEl(dialogState.anchorEl);
      setSelectedText(filterDefaultTitle(dialogState.selectedText));

      if (dialogState.anchorEl) {
        let width = dialogState.anchorEl.parentNode.offsetWidth;
        if (width) {
          setWidth(width - 16);
        }
      }

      if (dialogState.trigger == 'entrance') {
        setEntranceWithAI(true);
      }
    } else {
      const path = dialogState.path || [editor.selection?.focus.path[0] || 0];

      const domNode = toDOMNode(editor, getNode(editor, path));
      if (!domNode) {
        return handleClose(true)
      }

      setAnchorEl(domNode);
      setWidth(domNode.offsetWidth);

      if (dialogState.trigger === 'doctool') {
        itemSelected({ action: dialogState.action });
      } else if (dialogState.trigger === 'entrance') {
        setSelectedText(filterDefaultTitle(dialogState.selectedText));
        setEntranceWithAI(true);
      } else if (dialogState.trigger === 'ballonToolbar') {
        if (!editor.getFragment()) {
          handleClose(true);
          return;
        }
        setSelectedText(slateToMD(editor.getFragment()));
        setTextSelection(editor.selection);
      } else if (dialogState.trigger === 'blockHandler') {
        setSelectedText(slateToMD([getNode(editor, path)]))
        setTextSelection({
          anchor: {
            offset: 0,
            path
          },
          focus: {
            offset: 0,
            path
          }
        })
      } else if (dialogState.trigger === 'cmd') {
        const focus = editor.selection?.focus;
        const selected = getSelectedBlocksForCmd(focus);
        let selectedText = slateToMD(selected.children);

        let title = docs.byId[dialogState.hid]?.title;
        title = filterDefaultTitle(title?.trim());

        if (selected.usingTitle && title) {
          selectedText = '# ' + title + '\n\n' + selectedText;
          setTitleSelected(true);
        }

        setSelectedText(selectedText);
        setTextSelection({
          anchor: {
            offset: 0,
            path: [selected.range.start]
          },
          focus: {
            offset: 0,
            path: [selected.range.end]
          }
        });
      }
    }
  }, [dialogState]);

  React.useEffect(() => {
    set_attach_selected_content(selectedText?.trim())

    if (dialogState.action) {
      return itemSelected({ action: dialogState.action });
    }
  }, [selectedText])

  React.useEffect(() => {
    if (!entranceWithAI) {
      return;
    }
    let form;
    if (dialogState.caller == 'slides') {
      let item = assistant_items.find(item => item.action === 'slideshow' && item.objTypes.includes('slides'));
      form = {
        item,
        args: cloneDeep(item.args || [])
      }
      // itemSelected({ action: 'slideshow' });
    } else if (dialogState.caller == 'plate') {
      let item = drafter;
      let sub_item;
      if (!dialogState.action) {
        sub_item = drafter.sub_items.find(subItem => subItem.value === 'draft_outline');
      } else {
        item = get_ai_prompt_item(dialogState.action);
        if (item.action === 'draft') {
          sub_item = drafter.sub_items.find(subItem => subItem.value === (dialogState.sub_item || 'draft_outline'));
        }
      }

      form = {
        item,
        sub_item,
        args: cloneDeep(sub_item?.args || item.args || [])
      };
    }

    if (form) {
      let index = form.args?.findIndex(arg => arg.name == 'topic');
      if (index > -1) {
        form.args[index].value = selectedText;
      } else {
        for (index = 0; index < form.args?.length; index++) {
          if (['text', 'textline'].includes(form.args[index].type)) {
            form.args[index].value = selectedText;
            break;
          }
        }
      }
      setForm(form);
    }
  }, [entranceWithAI])

  React.useEffect(() => {
    if (!ai_items) {
      return;
    }

    if (!selectedText?.trim()) {
      return setAvaliableAssistantItems(ai_items.filter(item => [3, 5].includes(item.group) || item.type == 'user_installed'));
    }

    setAvaliableAssistantItems(ai_items);
  }, [ai_items, selectedText])

  React.useEffect(() => {
    if (!avaliableAssistantItems) return;
    const extended = avaliableAssistantItems
      .filter(item => item.objTypes.includes(dialogState.objType || 'markdown') || loginUser?.occupation === 'teacher' && item.objTypes.includes('edu'))
    // .flatMap((item) => {
    //   if (!item.content_type) {
    //     return item;
    //   }

    //   return item.content_type.map((type, index) => {
    //     return {
    //       ...item,
    //       label: item.label.replace('{content_type}', type),
    //       draftingType: item.draft_types && item.draft_types[index],
    //       prompt_user: item.prompt_user.replace('{content_type}', type)
    //     };
    //   });
    // });

    setExtendedAssistantItems(extended);
  }, [avaliableAssistantItems, dialogState.objType]);

  const isFillingForm = () => {
    if (!form) {
      return false;
    }

    return form.args && form.args.findIndex(arg => !!arg.value) > -1
  }
  const handleClose = (forced) => {
    if (!!forced || (!aiResponse && !loading && !searchText && !isFillingForm())) {
      dispatch({ type: AI_ASSISTANT_DIALOG, value: { visible: false } });
    } else {
      return setConfirmMessage({
        onConfirm: () => handleClose(true),
        content: intl.formatMessage({ id: loading ? 'confirm_no_wait' : 'confirm_close_ai' })
      })
    }
  }

  const handleBack = (forced) => {
    if (!forced && (loading || aiResponse && !aiResponse.err || isFillingForm())) {
      return setConfirmMessage({
        onConfirm: () => handleBack(true),
        content: intl.formatMessage({ id: loading ? 'confirm_no_wait' : 'confirm_discard_ai_content' })
      })
    }

    if (loading) {
      setCancelled(doing);
    }

    if (!doing || !aiResponse && form) {
      setForm(null);
      setContext(null);
      set_attach_selected_content(selectedText?.trim())
    }

    if (doing?.action === 'query' && doing.searchText) {
      setUserInput(doing.searchText);
    }

    setLoading(false);
    setDoing(null);
    setAiResponse(null);
    setErrMsg(null);
    setConfirmMessage(null);
  }

  React.useEffect(() => {
    selectedItemRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'center'
    });
  }, [selectedItemRef]);

  const elmRefs = React.useRef({});
  React.useLayoutEffect(() => {
    if (!elmRefs) return;

    elmRefs[itemTargeted] && elmRefs[itemTargeted].current.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    });

    const selectedItem = elmRefs.current[itemTargeted];
    selectedItem && selectedItem.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    });

    close_sub_menu()
    // let item = groupedAssistantItems.find(it => it.order == itemTargeted);
    // set_sub_menu_items(item?.sub_items);

  }, [itemTargeted]);

  const sub_menu_item_refs = React.useRef([]);
  React.useLayoutEffect(() => {
    if (!sub_menu_item_refs) return;

    sub_menu_item_refs[sub_menu_item_targeted] && sub_menu_item_refs[sub_menu_item_targeted].current.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    });

    const selectedItem = sub_menu_item_refs.current[sub_menu_item_targeted];
    selectedItem && selectedItem.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    });
  }, [sub_menu_item_targeted]);

  React.useEffect(() => {
    set_sub_menu_visible(sub_menu_anchor && sub_menu_items);
  }, [sub_menu_anchor, sub_menu_items])

  const [confirm_option_switcher, set_confirm_option_switcher] = React.useState();
  const [confirm_option_enter, set_confirm_option_enter] = React.useState();

  React.useEffect(() => {
    if (confirmMessage) return;

    set_confirm_option_enter(0);
    set_confirm_option_switcher(0);
  }, [confirmMessage])

  const onKeyPress = (event) => {
    const itemsCount = sub_menu_visible ? sub_menu_items?.length : filteredAssistantItems.length;

    if (!event.shiftKey && event.key === 'Enter') {
      if (confirmMessage) {
        set_confirm_option_enter(Math.random() + 1);
      } else if (sub_menu_visible) {
        if (sub_menu_item_targeted < 0 || sub_menu_item_targeted >= itemsCount) {
          return;
        }

        sub_menu_item_clicked(sub_menu_items[sub_menu_item_targeted]);
      } else {
        if (!open_sub_menu(itemTargeted)) {
          if ((itemTargeted < 0 || itemTargeted >= itemsCount)) {
            !form?.args?.find(arg => arg.type == 'text') && askAI();
            return;
          }

          itemSelected({ order: itemTargeted });
          setItemTargeted(0);
        }
      }

      event.stopPropagation();
      event.preventDefault();
    }
  }

  const onKeyDown = (event) => {
    const itemsCount = sub_menu_visible ? sub_menu_items?.length : filteredAssistantItems.length;
    if (event.key === 'Escape') {
      event.stopPropagation();
      event.preventDefault();

      handleClose();
    } else if (itemsCount && !event.shiftKey && event.key === 'ArrowDown' && !['textarea'].includes(event.target.tagName.toLowerCase())) {
      if (!sub_menu_visible) {
        setItemTargeted(prevState => {
          if (prevState < itemsCount - 1) {
            return prevState + 1;
          }

          return 0;
        })
      } else {
        set_sub_menu_item_targeted(prevState => {
          if (prevState < itemsCount - 1) {
            return prevState + 1;
          }

          return 0;
        })
      }
    } else if (itemsCount && !event.shiftKey && event.key === 'ArrowUp' && !['textarea'].includes(event.target.tagName.toLowerCase())) {
      if (!sub_menu_visible) {
        setItemTargeted(prevState => {
          if (prevState > 0) {
            return prevState - 1;
          }

          return itemsCount - 1;
        })
      } else {
        set_sub_menu_item_targeted(prevState => {
          if (prevState > 0) {
            return prevState - 1;
          }

          return itemsCount - 1;
        })
      }
    } else if (!event.shiftKey && event.key === 'ArrowRight') {
      if (confirmMessage) {
        set_confirm_option_switcher(Math.random() + 1);
      } else {
        if (itemTargeted < 0) {
          return;
        }

        open_sub_menu(itemTargeted);
      }
    } else if (!event.shiftKey && event.key === 'ArrowLeft') {
      if (confirmMessage) {
        set_confirm_option_switcher(Math.random() + 1);
      } else {
        if (sub_menu_anchor) {
          close_sub_menu()
        }
      }

      if (sub_menu_visible) {
        event.stopPropagation();
        event.preventDefault();
      }
    }
  }

  const open_sub_menu = (parent_item) => {
    let item = groupedAssistantItems.find(it => it.order == parent_item);
    if (!item?.sub_items) {
      return false;
    }

    if (sub_menu_visible && sub_menu_parent === parent_item) {
      close_sub_menu()
      return false
    }

    set_sub_menu_parent(parent_item);
    set_sub_menu_items(item?.sub_items);
    set_sub_menu_anchor(elmRefs.current[parent_item]);

    return true;
  }

  const close_sub_menu = () => {
    set_sub_menu_visible(false);
    set_sub_menu_anchor(null);
    set_sub_menu_items(null);
  }

  const itemSelected = ({ order, action }) => {
    let item = !action ? groupedAssistantItems.find(it => it.order == order)
      : (ai_items.find(it => it.action == action) || groupedAssistantItems.find(it => it.action == action));
    item = item || {
      action: dialogState.action
    }

    if (item.action === 'add_prompt') {
      gotoPrompts()
      return;
    }

    if (typeof item.action === 'function') {
      return item.action();
    } else if (item.sub_items) {
      open_sub_menu(order);
      return;
    }

    if (item.args?.length > 0 || (!selectedText?.trim() && (item.prompt_user || item.prompt)?.includes('{{selected_text}}'))) {
      setUserInput('');
      setForm({
        item,
        args: item.args || []
      })
      textInputRef?.current?.focus()
      return;
    }

    aiAction({ action: item.action, aiResponse, disableContext: true });
  }

  const sub_menu_item_clicked = (item) => {
    close_sub_menu();

    const parent_item = groupedAssistantItems.find(it => it.order == sub_menu_parent);
    if (!parent_item) return;

    if (item.args?.length > 0 || parent_item.args?.length > 0
      || item.type == 'user_installed' && (!selectedText?.trim() && item.prompt_user?.includes('{{selected_text}}')) ||
      parent_item.type == 'user_installed' && (!selectedText?.trim() && parent_item.prompt_user?.includes('{{selected_text}}'))) {
      setUserInput('');
      setForm({
        item: parent_item,
        sub_item: item,
        args: item.args || parent_item.args || []
      });

      return;
    }

    let text = selectedText;
    if (aiResponse) {
      text = aiResponse.content;
    }

    aiAction({ action: parent_item.action, aiResponse, sub_item: item, disableContext: true })
  }

  const askAI = () => {
    if (context?.type === 'choose_a_doc') {
      if (!context.value) {
        return setContextErrMsg(intl.formatMessage({ id: 'context_no_doc_choosen' }));
      } else {
        let contextText = context.value.type == 'doc' && slateToMD(context.value.blocks)?.trim()
          || context.value.type == 'slides' && context.value.markdown?.trim();

        if (!contextText) {
          return setContextErrMsg(intl.formatMessage({ id: 'context_doc_no_content' }));
        }
      }
    }

    if (searchText && !loading) {
      aiAction({ action: 'query', userInput: textInputRef.current?.innerText || textInputRef.current?.textContent, aiResponse });
    } else if (form) {
      const validArgs = form.args?.filter(arg => !arg.disabled);
      const required_args = validArgs?.filter(arg => arg.required);

      if (required_args?.length > 0) {
        for (const arg of required_args) {
          if (!arg.value?.trim()) {
            return showErrorMsg('missing_required_data');
          }
        }
      } else {
        if (!getContextContent() && !validArgs?.find(arg => !!arg.value?.trim())) {
          return showErrorMsg('missing_one_data');
        }
      }

      aiAction({ action: form.item.action, sub_item: form.sub_item, aiResponse });
    } else if (!searchText && !aiResponse) {
      setErrMsg(intl.formatMessage({ id: 'should_text_or_select_item' }))
    } else if (aiResponse) {
      setErrMsg(intl.formatMessage({ id: 'should_select_item' }))
    }
  }

  const [copied, setCopied] = React.useState();
  const copy = () => {
    navigator.clipboard.writeText(aiResponse.content).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 3000)
    });
  }

  const [loading, setLoading] = React.useState();
  const [subTasking, setSubTasking] = React.useState();
  const [cancelled, setCancelled] = React.useState();
  const [doing, setDoing] = React.useState();
  const [form, setForm] = React.useState();
  const [aiRespItem, setAiRespItem] = React.useState();
  const [aiResponse, setAiResponse] = React.useState();
  const [prevAIResponse, setPrevAIResponse] = React.useState();
  const [aiResultRefresher, setAiResultRefresher] = React.useState();

  React.useEffect(() => {
    // if (!form?.sub_item?.prompt_user && !form?.item?.prompt_user) return;

    if (!(form?.sub_item?.prompt_user || form?.item?.prompt_user || form?.item?.prompt)?.includes('{{selected_text}}')) {
      return;
    }

    setForm(prevForm => {
      let args = prevForm.args || [];
      let dynamic_arg = args.find(arg => arg.name == 'selected_text');
      if (dynamic_arg) {
        dynamic_arg.disabled = !!attach_selected_content;
      } else {
        args.unshift({
          disabled: !!attach_selected_content,
          name: 'selected_text',
          type: 'text',
          label: intl.formatMessage({ id: 'ask_ai_about_selected' }),
          required: true,
          hint: intl.formatMessage({ id: 'ask_ai_about_given_text_hint' })
        });
      }

      return {
        ...prevForm,
        args
      }
    })

  }, [form?.sub_item, form?.item, attach_selected_content])

  // const extractJSONFromString = React.useCallback((str) => {
  //   let stack = [];
  //   let startIndex = -1;
  //   let endIndex = -1;

  //   for (let i = 0; i < str.length; i++) {
  //     if (str[i] === "{") {
  //       if (stack.length === 0) {
  //         startIndex = i;
  //       }
  //       stack.push("{");
  //     } else if (str[i] === "}" && stack.length > 0) {
  //       stack.pop();
  //       if (stack.length === 0) {
  //         endIndex = i;
  //         break; // 找到第一个完整的 JSON 对象，提前结束循环
  //       }
  //     }
  //   }

  //   if (startIndex !== -1 && endIndex !== -1) {
  //     const jsonString = str.substring(startIndex, endIndex + 1);

  //     try {
  //       const parsedJSON = JSON5.parse(jsonString);
  //       return parsedJSON;
  //     } catch (error) {
  //       console.error("Invalid JSON format:", error);
  //       return null; // JSON 解析错误，返回 null 或其他合适的值
  //     }
  //   } else {
  //     return null; // 如果字符串中没有找到完整的 JSON 对象，返回 null 或其他合适的值
  //   }
  // }, [])

  const processSlideContent = React.useCallback((content) => {
    if (Array.isArray(content)) {
      return content.join('\n\n').replace(/\n\n(\d+\.\s)/g, '\n$1').replace(/\n\n([-*]\s)/g, '\n$1');
    } else if (typeof content === 'object') {
      return Object.keys(content).map(key => key + ':' + content[key]).join('\n\n').replace(/\n\n(\d+\.\s)/g, '\n$1').replace(/\n\n([-*]\s)/g, '\n$1');
    }

    return content;
  }, []);

  const makeSlide = React.useCallback((slide) => {
    // return '## ' + slide.title + '\n\n' + processSlideContent(slide.content) + (slide.notes?.length ? '\n\nNotes:\n\n' + processSlideContent(slide.notes) : '')
    return `${!!slide.title && ('## ' + slide.title + '\n\n') || ''}${slide.content}\n\nNotes: ${slide.notes}`;
  }, []);

  const generateDone = (aiRespItem) => {
    setSubTasking(false);
    setLoading(false);
    setItemTargeted(-1);
    setUserInput('');

    setCopied(false);
    // setForm(null);
    // setContext(null);

    if (aiRespItem?.subTask) {
      setForm(form => {
        let args = cloneDeep(form.args);
        let arg = args.find(arg => arg.name == (aiRespItem.subTask.to_arg || aiRespItem.subTask.dynamic_arg?.name));
        if (arg) {
          arg.orignalValue = undefined;
        }

        return {
          ...form,
          args
        }
      })
    }
    // setForm(null);
  }

  const initContent = (aiRespItem) => {
    const { subTask } = aiRespItem;

    if (subTask) {
      setForm(form => {
        let args = cloneDeep(form.args);
        let arg = args?.find(arg => arg.name == (subTask.to_arg || subTask.dynamic_arg?.name));
        if (arg) {
          arg.orignalValue = arg.value;
        }

        return {
          ...form,
          args
        }
      })
    }

    let content = aiRespItem.action !== 'continue' ? '' : (aiRespItem.menuItem?.id === 'retry' ? (prevAIResponse?.content || '') : (aiResponse?.content || '')) + '\n\n'
    return {
      ...aiRespItem,
      content
    }
  }

  React.useEffect(() => {
    if (!aiRespItem) {
      return;
    }

    if (aiRespItem.sessionId && cancelled?.sessionId === aiRespItem.sessionId) {
      return;
    }

    if (!aiRespItem.stream) {
      if (dialogState.trigger === 'doctool' && dialogState.action === 'xSlides') {
        if (aiRespItem.doc) {
          dispatch({
            type: PRIVATE_DOCS_ACTIONS.added,
            _id: aiRespItem.doc._id,
            item: Object.assign({}, aiRespItem.doc),
            params: { pageBy: 'orderFactor' },
            key: loginUser.workingOrgId
          })

          dispatch({
            type: DOC_ACTIONS.received,
            item: aiRespItem.doc
          })

          dispatch({
            type: 'CONFIRM_DIALOG', value: {
              visible: true,
              handleConfirm: () => history.push({ pathname: '/slidesEditor', state: { hid: aiRespItem.doc.hid, hideHeader: true } }),
              content: intl.formatMessage({ id: 'confirm_open_ai_generated_slides' })
            }
          });

          handleClose(true);
          return;
        }
      }
    }

    if (aiRespItem.err) {
      generateDone(aiRespItem);

      setAiResponse({
        ...aiRespItem
      })
    } else {
      let content = aiRespItem.content;
      if (!aiRespItem.stream) {
        content = initContent(aiRespItem)?.content + content;
      }

      if (aiRespItem.done || !aiRespItem.stream) {

        if (dialogState.action === 'continue') {
          operation('insertBelow', aiRespItem)
          return;
        }

        setPrevAIResponse(aiResponse);

        if (doing?.action === 'slide') {
          try {
            let generated = extractJSONFromString(content)?.generated;
            if (generated?.slide) {
              content = makeSlide(generated.slide) + '\n\n'
            }
          } catch (err) {
            console.log('err........', err)
          }
        } else if (['slideshow', 'slides_optimize'].includes(doing?.action)) {
          try {
            let generated = extractJSONFromString(content).generated;;
            if (generated?.slides) {
              content = generated.slides.map(slide => {
                return makeSlide(slide);
              }).join('\n\n---\n\n')
            }

            if (doing.action === 'slides_optimize' && generated.optimization_explained) {
              content = generated.optimization_explained + '\n\n---\n\n' + content;
            }
          } catch (err) {
            console.log('err........', err)
          }
        }

        const svgRegex = /<svg[\s\S]*?<\/svg>/gi;
        const codeBlockRegex = /```svg[\s\S]*?```/gi;
        if (svgRegex.test(content) && !codeBlockRegex.test(content)) {
          content = content.replace(svgRegex, (match) => {
            return `\n\`\`\`svg\n${match}\n\`\`\`\n`;
          });
        }

        generateDone(aiRespItem);
      }

      if (doing?.subTask) {
        setForm(form => {
          let args = cloneDeep(form.args);
          let arg = args?.find(arg => arg.name == (doing.subTask.to_arg || doing.subTask.dynamic_arg?.name));
          if (arg) {
            if (arg.readOnly) {
              arg.value = aiRespItem.content;
            } else {
              arg.value = (arg.orignalValue?.trim() ? arg.orignalValue.trim() + "\n" : "") + aiRespItem.content;
            }
          }

          return {
            ...form,
            args
          }
        })

        return;
      }

      setAiResponse({
        ...aiRespItem,
        content: content.replace(/^````|````$/g, ''),
        groundingMetadata: aiRespItem.groundingMetadata
      });
    }
  }, [aiRespItem])

  React.useEffect(() => {
    if (contentContainerRef?.current) {
      contentContainerRef.current.scrollTop = contentContainerRef.current.scrollHeight
    }
  }, [contentContainerRef?.current?.scrollHeight])

  React.useEffect(() => {
    if (!aiResultRefresher) return;

    if (cancelled && cancelled.sessionId === aiResultRefresher.sessionId) {
      return;
    }

    dispatch(refreshAIResponse({ sessionId: aiResultRefresher.sessionId, isChat: false }, (item) => {
      if (item) {
        setAiRespItem({ ...item, menuItem: aiResultRefresher.menuItem });
      } else {
        if (new Date().getTime() - aiResultRefresher.sessionId > 110 * 1000) {
          setLoading(false);
          showErrorMsg('ai_timeout');
          return;
        }

        setTimeout(() => {
          setAiResultRefresher({ ...aiResultRefresher, refreshId: Math.random() })
        }, 5 * 1000)
      }
    }, 'aimodal'))
  }, [aiResultRefresher])

  const showErrorMsg = (err) => {
    setLoading(false);
    setSubTasking(false);

    setErrMsg(intl.formatMessage({ id: err }));
  }

  const isChinese = React.useCallback((str) => {
    var reg = /[\u4e00-\u9fa5]/g; //使用Unicode字符集范围来匹配中文字符
    return reg.test(str);
  }, [])

  const getContextContent = React.useCallback(() => {
    let contextText;
    if (context?.type == 'task_content_from_selected') {
      contextText = selectedText;
    } else if (context?.type == 'choose_a_doc' && context.value) {
      contextText = context.value.type == 'doc' && ('# ' + context.value.title + '\n\n' + extractContextFromBlocks(context.value.blocks))
        || context.value.type == 'slides' && context.value.markdown;
    } else if (context?.type == 'current_doc') {
      contextText = dialogState.caller === 'plate' && ('# ' + docs.byId[hid]?.title + '\n\n' + extractContextFromBlocks(docs.byId[hid]?.blocks))
        || dialogState.caller === 'slides' && dialogState.pageContent;
    } else {
      contextText = attach_selected_content && selectedText;
    }

    return contextText;
  }, [context, selectedText, dialogState, docs, hid, attach_selected_content])

  const aiAction = ({ menuItem, action, userInput = '', sub_item, subTask, aiResponse, disableContext }) => {
    const sessionId = new Date().getTime();
    setAiRespItem(null);

    if (!userInput?.trim() && !selectedText?.trim() && !aiResponse?.content && action != 'xSlides' && !form) {
      return showErrorMsg('no_text')
    } else if (using_model.level !== 'private' && (selectedText?.length > TEXT_LEN_LIMIT && action != 'xSlides' || userInput?.length > TEXT_LEN_LIMIT || aiResponse?.content?.length > TEXT_LEN_LIMIT)) {
      return showErrorMsg('text_too_long')
    } else {
      setErrMsg(null);
    }

    let actionItem = get_ai_prompt_item(action);

    if (!isDesiredModal(actionItem, sub_item, using_model)) {
      return;
    }

    let newDoing = { action, sessionId, sub_item, form, subTask, userInput, aiResponse, disableContext };
    if (actionItem) {
      newDoing.label = actionItem.label;
    } else {
      newDoing.label = doing?.label;
    }

    setDoing(newDoing);

    if (subTask) {
      setSubTasking(subTask.action);
    } else {
      setLoading(true);
    }

    if (!actionItem) {
      actionItem = {
        action
      }
    }

    // console.log('action item................', actionItem, form)

    // let content = text;
    let content = userInput;
    if (aiResponse) {
      //has aiResonpse means action after AI response, otherwise means fresh task for AI.
      if (userInput) {
        content = 'Given text:````' + aiResponse.content + '````\n\nUser Prompt: ````' + userInput + '````';
      } else {
        content = aiResponse.content;
      }
    } else {
      if (actionItem.prompt_user) {
        content = actionItem.prompt_user
      }
      if (form) {
        if (subTask) {
          if (subTask.dynamic_arg && !form.args?.find(arg => arg.name == subTask.dynamic_arg.name)) {
            setForm(prevState => {
              let args = [...prevState.args];
              args.unshift(subTask.dynamic_arg)

              return {
                ...prevState,
                args
              }
            })
          }

          content = subTask.prompt_user;
        } else {
          content = form.sub_item?.prompt_user || form.item?.prompt_user || content;
        }
        if (form.sub_item) {
          content = content.replaceAll('{{sub_item}}', form.sub_item.label);
        }

        let unused_args = [];
        for (let arg of form.args) {
          if (!arg.disabled && !arg.readOnly) {
            if (content.indexOf(`{{${arg.name}}}`) > -1) {
              content = content.replaceAll(`{{${arg.name}}}`, '````' + (arg.value || intl.formatMessage({ id: 'not_provided' })) + '````');
            } else if (arg.value) {
              unused_args.push(arg)
            }
          }
        }

        if (unused_args.length > 0) {
          content = content + '.\n' + unused_args.map(arg => {
            return arg.label + ':````' + arg.value + '````';
          }).join('\n');
        }
      }

      if (selectedText) {
        if (!content) {
          content = selectedText;
        } else if (content.includes('{{selected_text}}')) {
          content = content.replaceAll('{{selected_text}}', '````' + selectedText + '````');
        } else if (attach_selected_content) {
          content = intl.formatMessage({ id: 'context' }) + ': ' + '````' + selectedText + '````.' + '\n' + content
        }
      }

      let contextText;

      if (!disableContext && context) {
        let contextText = getContextContent();

        if (contextText) {
          content = content + '.\n' + intl.formatMessage({ id: 'context' }) + ': ' + '````' + contextText + '````.'
        }
      }

      if (disableContext) {
        setContext(null);
      }

      // content = actionItem.group === 9 ? actionItem.prompt.replace('[TEXT]', `"${text}"`) : (form ? drafting.prompt_user + text : text);
      // if (action === 'query' && filledSelection || actionItem.type === 'user_installed' && (filledSelection || actionItem.content_source === 'selected')) {
      // if (contextText || attach_selected_content) {
      //   content += '\n\nThe given text is in Markdown format, please also reply in Markdown format'
      // }
    }

    if (using_model?.level === 'private') {
      let messages = [{
        role: 'user',
        content,
      }];

      let prompt = actionItem.type != 'user_installed' && actionItem.prompt;
      if (actionItem.type != 'user_installed' && actionItem?.sub_items && actionItem?.prompts) {
        prompt = actionItem.prompts[actionItem.sub_items.findIndex(item => item.value === sub_item?.value)];
      }

      // console.log('prompts..........', actionItem, text)

      if (prompt && !subTask) {
        prompt += '. Reply with markdown format';

        messages.unshift({
          role: 'system',
          content: prompt
        });
      }

      if (!action?.includes('translate')) {
        if (isChinese(selectedText || userInput)) {
          messages[0].content = messages[0].content + `. Respond in Chinese`;
        } else {
          messages[0].content = messages[0].content + `. Respond in the same language with the given text or context`;
        }
      }

      let params = {
        model: using_model.model
      }

      if (['openai', 'groq', 'openai_compatible'].includes(using_model.provider)) {
        params.messages = messages;
        if (actionItem?.temperature) {
          params.temperature = actionItem?.temperature;
        }
      } else if (using_model.provider == 'gemini') {
        let human_content = messages.length == 1 ? messages[0].content : (messages[0].content + '.\nThe given text is:' + messages[1].content);
        // console.log('human content:', human_content)
        params.contents = [{
          parts: [{ text: human_content }]
        }];
        params.generationConfig = {
          temperature: actionItem?.temperature
        }
      } else if (using_model.provider == 'anthropic') {
        params.max_tokens = 3000;

        let system = messages.find(msg => msg.role == 'system');
        if (system) {
          params.system = system.content
        }
        params.messages = messages.filter(msg => msg.role != 'system');
      } else {
        return showErrorMsg('invalid_api_settings');
      }

      // console.log('action...........', action, params)
      // console.log('messages to open ai......', actionItem, prompt, messages)
      if (['openai', 'groq', 'gemini', 'openai_compatible'].includes(using_model.provider)) {
        let streamGenerator = openAIStreamGenerate;
        if (using_model.provider == 'gemini') {
          streamGenerator = geminiStreamGenerate;
        }

        streamGenerator(using_model.endpoint, using_model.token, params, () => {
          setAiRespItem(initContent({
            sessionId,
            action,
            menuItem,
            subTask,
            stream: true
          }))
        }, (chunkText) => {
          setAiRespItem(prevState => {
            return {
              ...prevState,
              content: (prevState?.content || "") + chunkText,
            }
          })
        }, (errMsg) => {
          setAiRespItem(prevState => {
            return {
              ...prevState,
              err: errMsg
            }
          })
        }, () => {
          setAiRespItem(prevState => {
            return {
              ...prevState,
              done: true
            }
          })
        })
      } else {
        dispatch(AI_API_CALLER[using_model.provider](using_model.endpoint, using_model.token, params, (item) => {
          setAiRespItem({
            content: removeQuotes(item),
            sessionId,
            action,
            menuItem,
            subTask
          });
        }, (errMsg) => {
          setLoading(false);
          setSubTasking(false);
          setErrMsg("Failed, reason: " + intl.formatMessage({ id: errMsg }));
          setAiRespItem(prevState => {
            return {
              ...prevState,
              err: errMsg
            }
          })
        }))
      }
    } else {
      dispatch(callAIAssist({
        data: {
          model: using_model?.value,
          model_level: using_model?.level,
          use_search: ai_use_search,
          action: actionItem.type === 'user_installed' || subTask ? 'query' : action,
          sub_item: sub_item?.value,
          objType: dialogState.objType || actionItem?.objTypes?.length > 0 && actionItem.objTypes[0] || 'markdown',
          content,
          hid: dialogState.hid,
          sessionId,
          app: 'funblocks_web'
        }
      }, (item) => {
        setAiRespItem({ ...item, menuItem, subTask });
      }, (err) => {
        if (err === 'TIMEOUT' && (!cancelled || cancelled.sessionId != sessionId)) {
          setTimeout(() => {
            // refreshAIResp(sessionId, menuItem, action);
            setAiResultRefresher({ sessionId, menuItem, action, refreshId: Math.random() })
          }, 3 * 1000)
        } else {
          setLoading(false);
          setSubTasking(false);
        }

        setAiRespItem(prevState => {
          return {
            ...prevState,
            err
          }
        })
        // setAiRespItem({
        //   err
        // })
      }))
    }
  }

  const retry = () => {
    if (!doing) return;

    setErrMsg(null);

    if (doing.action === 'query') {
      setAiResponse(null);
      setUserInput(doing.userInput);
      return;
    } else if (doing.form) {
      setAiResponse(null);
      setForm(doing.form);
      return;
    }

    aiAction({ ...doing, menuItem: { id: 'retry' } });
  }

  const operation = (op, aiResponse) => {
    dispatch({
      type: AI_ASSISTANT_DIALOG,
      value: {
        ...dialogState,
        visible: false,
        doing,
        operation: op,
        response: aiResponse,
        usingTitle
      }
    })
  }

  const responseMenuItems = React.useMemo(() => [{
    id: 'replace',
    label: intl.formatMessage({ id: 'replace_selection' }),
    icon: <Check size={18} color='#777' />,
    action: () => operation('replace', aiResponse),
    group: 11,
  }, {
    id: 'close',
    label: intl.formatMessage({ id: 'discard' }),
    icon: <Close size={18} color='#777' />,
    action: handleClose,
    group: 11,
  }, {
    id: 'insertBelow',
    label: intl.formatMessage({ id: 'insert_below' }),
    icon: <ArrowToBottom size={18} color='#777' />,
    action: () => operation('insertBelow', aiResponse),
    group: 11,
  }, {
    id: 'retry',
    label: intl.formatMessage({ id: 'try_again' }),
    icon: <Refresh size={17} color='#777' />,
    action: () => retry(),
    group: 11,
  }, {
    id: 'continue',
    action: 'continue',
    icon: <Wand size={18} color='#777' />,
    group: 12,
  }, {
    id: 'extend',
    icon: <Expand size={18} color='#777' />,
    action: 'extend',
    group: 12,
  }, {
    id: 'translate',
    action: 'translate',
    icon: <Translate size={18} color='#777' />,
    group: 12,
  }, {
    id: 'copy',
    label: intl.formatMessage({ id: copied ? 'copied' : 'copy_generated_content' }),
    icon: copied ? <Check size={18} color='green' /> : <Copy size={18} color='#777' />,
    action: () => copy(),
    group: 13,
  }, {
    id: 'back',
    label: intl.formatMessage({ id: 'back' }),
    icon: <ArrowBack size={17} color='#777' />,
    action: handleBack,
    group: 13,

  }].map(item => {
    let existing_item = assistant_items?.find(i => i.action === item.action);
    if (!existing_item) {
      return item;
    }

    return {
      ...existing_item,
      ...item
    }
  }), [operation, aiResponse, retry, handleBack, handleClose, assistant_items]);

  const moveElementToHeader = React.useCallback((array, pos) => {
    if (array.length < pos + 1) return;

    let element = array.splice(pos, 1)[0];
    array.unshift(element);
  }, [])

  const groupMenuItems = React.useCallback((items) => {
    let grouped = Array.from({ length: Math.max(...items.map(item => item.group)) + 1 }, () => []);

    items.forEach(item => {
      if (typeof item.group == 'number') {
        let pos = item.group || 0;
        grouped[pos].push({ ...item });
      }
    });

    //move generate slides group to header
    moveElementToHeader(grouped, 5);

    let index = 0;
    for (let i = 0; i < grouped.length; i++) {
      const group = grouped[i];
      for (let j = 0; j < group.length; j++) {
        const item = group[j];
        item.order = index;
        index++;
      }
    }

    grouped = grouped.flatMap((group, i) => {
      if (!group || group.length === 0) {
        return [];
      }

      const item_group = assistant_items_groups?.find(item => item.group === group[0].group);

      if (item_group) {
        group.unshift({
          type: 'groupName',
          label: item_group.label,
          group: i
        })
      }

      if (i > 0) {
        group.unshift({
          type: 'divider'
        })
      }

      return group;
    });

    return grouped;
  }, [assistant_items_groups]);

  React.useEffect(() => {
    if (!aiResponse && !extendedAssistantItems) return;

    let itemsFiltered;
    if (loading) {
      if (['continue', 'optimize'].includes(dialogState.action)) {
        itemsFiltered = responseMenuItems.filter(item => ['close'].includes(item.id));
      } else {
        itemsFiltered = responseMenuItems.filter(item => ['back', 'close'].includes(item.id));
      }
    } else if (!aiResponse) {
      itemsFiltered = extendedAssistantItems;

      if (form) {
        itemsFiltered = responseMenuItems.filter(item => ['back', 'close'].includes(item.id));
      } else if (['continue', 'optimize'].includes(dialogState.action)) {
        itemsFiltered = [];
      }
    } else {
      itemsFiltered = responseMenuItems;

      if (aiResponse.err) {
        itemsFiltered = itemsFiltered.filter(item => !['copy', 'continue', 'extend', 'insertBelow', 'replace', 'translate'].includes(item.id));
      }
      if (dialogState.action === 'continue') {
        itemsFiltered = itemsFiltered.filter(item => ['close'].includes(item.id));
      } else if (dialogState.trigger === 'entrance') {
        itemsFiltered = itemsFiltered.filter(item => !['back'].includes(item.id));
      } else if (dialogState.trigger === 'doctool' && dialogState.action === 'xSlides') {
        itemsFiltered = itemsFiltered.filter(item => ['retry', 'close'].includes(item.id));
      } else if (dialogState.caller === 'slides' && ['speechscript', 'speakernotes'].includes(doing?.action)) {
        itemsFiltered = itemsFiltered.filter(item => !['replace'].includes(item.id));
      } else if (dialogState.caller === 'slides' && ['keypoints'].includes(doing?.action)) {
        itemsFiltered = itemsFiltered.map(item => {
          if (item.id === 'replace') {
            item.label = intl.formatMessage({ id: 'replace' });
          } else if (item.id === 'insertBelow') {
            item.label = intl.formatMessage({ id: 'insert' });
          }

          return item;
        })
      } else if (doing?.action === 'title') {
        itemsFiltered = itemsFiltered.filter(item => [dialogState.caller === 'slides' ? 'insertBelow' : 'replace', 'retry', 'translate', 'back', 'close'].includes(item.id)).map(item => {
          if (item.id === 'replace') {
            item.label = intl.formatMessage({ id: 'replace_title' });
          } else if (item.id === 'insertBelow') {
            item.label = intl.formatMessage({ id: 'insert_title' });
          }

          return item;
        });
      }
    }

    itemsFiltered = itemsFiltered.filter(item => {
      // return ['back', 'close'].includes(item.id) || 
      return item.label?.toLowerCase().includes(searchText?.toLowerCase())
        || typeof item.action === 'string' && item.type !== 'user_installed' && item.action.toLowerCase().includes(searchText?.toLowerCase());
    });

    const replaceItem = itemsFiltered.find(item => item.id === 'replace');
    if (!replaceItem) {
      const insertBelowIndex = itemsFiltered.findIndex(item => item.id === 'insertBelow');

      if (insertBelowIndex !== -1) {
        const [insertBelowItem] = itemsFiltered.splice(insertBelowIndex, 1);
        itemsFiltered.unshift(insertBelowItem);
      }
    }

    setFilteredAssistantItems(itemsFiltered);

    let itemSelected = 0;
    if (!aiResponse && itemsFiltered.find(item => item.id === 'back')
      || aiResponse && itemsFiltered.length == 2
      || itemsFiltered.length == 0
    ) {
      itemSelected = -1;
    }
    // if (searchText) {
    //   itemSelected = itemsFiltered.findIndex(item => {
    //     return item.label?.toLowerCase().includes(searchText.toLowerCase())
    //       || typeof item.action === 'string' && item.type !== 'user_installed' && item.action.toLowerCase().includes(searchText.toLowerCase())
    //   })
    // }
    !copied && setItemTargeted(itemSelected);
  }, [searchText, extendedAssistantItems, aiResponse, loading, form, using_model, copied]);

  React.useEffect(() => {
    if (!filteredAssistantItems) return;

    setGroupedAssistantItems(groupMenuItems(filteredAssistantItems));
  }, [filteredAssistantItems])

  React.useLayoutEffect(() => {
    setTimeout(() => textInputRef?.current?.focus(), 100);
  }, [textInputRef?.current])

  React.useLayoutEffect(() => {
    setTimeout(() => form_input_ref?.current?.focus(), 100);
  }, [form_input_ref?.current])

  const gotoMarket = () => {
    // handleClose(true);
    dispatch({ type: SETTINGS_DIALOG, value: { visible: true, page: 'service_subscribe' } });
  }

  const inviteFriends = () => {
    // handleClose(true);
    dispatch({ type: INVITE_FRIENDS_DIALOG, value: { visible: true } });
  }

  const gotoAPISetting = () => {
    // handleClose(true);
    dispatch({ type: SETTINGS_DIALOG, value: { visible: true, page: 'api_settings' } });
  }

  const gotoPrompts = () => {
    dispatch({ type: SETTINGS_DIALOG, value: { visible: true, page: 'prompts' } });
  }

  const selected_content_chooseable = React.useMemo(() => {
    const itemsCount = groupedAssistantItems.length;
    if (searchText && (itemTargeted < 0 || itemTargeted >= itemsCount)) {
      return true;
    }

    const item = dialogState?.action ? assistant_items.find(i => i.action == dialogState.action) : groupedAssistantItems.find(item => item.order === itemTargeted);

    if (item?.prompt?.includes('{{selected_text}}') || item?.prompt_user?.includes('{{selected_text}}')
      || form?.item?.prompt_user?.includes('{{selected_text}}') || form?.sub_item?.prompt_user?.includes('{{selected_text}}')) {
      return false
    }

    if (form) {
      return true;
    }

    return item?.type !== 'user_installed' && item?.group == 3 || item?.type == 'user_installed' && !item?.prompt_user?.includes('{{selected_text}}')
  }, [itemTargeted, groupedAssistantItems, form, dialogState?.action])

  const contextOptions = React.useMemo(() => {
    return entranceWithAI && ['none', 'choose_a_doc'] || attach_selected_content && ['none', 'current_doc', 'choose_a_doc'] || ['none', 'task_content_from_selected', 'current_doc', 'choose_a_doc']
  }, [entranceWithAI, attach_selected_content])


  const [extendInput, setExtendInput] = React.useState();

  React.useEffect(() => {
    setExtendInput(prevState => prevState && !filteredAssistantItems?.length || !isInputZh && filteredAssistantItems?.length == 0)
  }, [filteredAssistantItems, isInputZh])
  // const extendInput = React.useMemo(() => !isInputZh && filteredAssistantItems?.length == 0, [filteredAssistantItems, isInputZh])

  const ai_magic_icon_and_status = React.useMemo(() => (<div style={{
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  }}>
    <div style={{ color: 'dodgerblue', marginRight: '6px' }} >
      <Magic size={20} />
    </div>
    {
      (loading || extendInput) &&
      <div style={{ marginRight: '6px', display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
        {loading && <CircularProgress size={18} />}
        <span style={{ marginLeft: '8px', marginRight: '8px', fontSize: '14px', color: 'gray', whiteSpace: 'nowrap' }}>
          {loading && intl.formatMessage({ id: 'askAI_doing' })}
          {!loading && extendInput && intl.formatMessage({ id: 'softbreak_tips' })}
        </span>
      </div>
    }
  </div>), [loading, intl, extendInput])

  // React.useEffect(() => {
  //   if(!!form) {
  //     set_attach_selected_content(false);
  //   }
  // }, [!!form])

  if (!anchorEl) {
    return <></>
  }

  const showContextFormInAskAI = !aiResponse && !loading && dialogState?.trigger !== 'entrance' && groupedAssistantItems?.length == 0;
  const contextForm = (<div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', whiteSpace: 'nowrap', columnGap: '6px' }}>
    <Tooltip title={intl.formatMessage({ id: 'prompt_context_desc' })} placement='bottom'>
      <div style={{ color: '#222', fontSize: 15 }}> {intl.formatMessage({ id: 'prompt_context' })}
        <span style={{ color: '#666', fontSize: '14px' }}> {'(' + intl.formatMessage({ id: 'arg_optional' }) + ')'} </span>
      </div>
    </Tooltip>

    <Selector
      options={(contextOptions).map((option, index) => {
        return {
          label: intl.formatMessage({ id: option }),
          value: option
        }
      })}
      value={context?.type || 'none'}
      onChange={(value) => {
        setContext(prevState => {
          return {
            ...(prevState || {}),
            type: value
          }
        })
      }}
    />
    {
      context?.type === 'choose_a_doc' &&
      <PageChooserMenu
        onSelect={(doc) => {
          if (!doc) {
            return;
          }

          if (doc.type == 'doc' && !doc.blocks || doc.type == 'slides' && !doc.markdown) {
            return dispatch(getDoc({ hid: doc.hid }, (item) => {
              setContext(prevState => {
                return {
                  ...prevState,
                  value: item
                }
              });
              setContextErrMsg(null);
            }, null, 'editor'));
          }

          setContext(prevState => {
            return {
              ...prevState,
              value: doc
            }
          });
          setContextErrMsg(null);
        }}
      />
    }
    {
      context?.type === 'choose_a_doc' &&
      context?.value &&
      <div
        style={{
          color: 'dodgerblue',
          cursor: 'pointer',
          fontSize: '14px'
        }}
        onClick={() => {
          let link = linkToPage(context.value);
          window.open(link, '_blank');
        }}
      >
        {context.value.title}
      </div>
    }
    {
      context?.type === 'choose_a_doc' &&
      contextErrMsg &&
      <div
        style={{
          color: 'red',
          fontSize: '14px'
        }}
      >
        {contextErrMsg}
      </div>
    }
  </div >);

  const renderSubTaskButton = (subTask) => {
    return <>
      {
        subTasking != subTask.action &&
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <div
            className='hoverButton'
            style={{
              paddingLeft: '6px', paddingRight: '6px',
              paddingTop: '1px', paddingBottom: '1px',
              borderRadius: '6px',
              fontSize: '14px',
              cursor: 'pointer', whiteSpace: 'nowrap', display: 'flex', alignItems: 'center', flexDirection: 'row',
              backgroundColor: subTasking ? '#ddd' : undefined,
              color: subTasking ? 'white' : undefined,
              // border: subTasking ? undefined : 'solid 1px dodgerblue'
            }}
            onClick={() => {
              if (subTasking) return;

              const validArgs = form.args.filter(arg => !arg.disabled);
              const required_args = validArgs?.filter(arg => arg.required);
              if (required_args?.length > 0) {
                for (const arg of required_args) {
                  if (!arg.value?.trim()) {
                    return showErrorMsg('missing_required_data');
                  }
                }
              } else {
                if (!getContextContent() && !validArgs.find(arg => !!arg.value?.trim())) {
                  return showErrorMsg('missing_one_data');
                }
              }

              aiAction({ action: form.item?.action, subTask, aiResponse })
            }}>
            {subTask.label}
            <div style={{ marginLeft: '6px', alignItems: 'center', display: 'flex' }} >
              <Magic size={16} />
            </div>
          </div>
        </div>
      }
      {
        subTasking == subTask.action && <>
          <CircularProgress size={18} />
          <span style={{ marginLeft: '8px', marginRight: '8px', fontSize: '14px', color: 'gray', whiteSpace: 'nowrap' }}>
            {intl.formatMessage({ id: 'askAI_doing' })}
          </span>
        </>
      }
    </>
  }

  return (
    <Popover
      open={dialogState.visible}
      onClose={() => handleClose(false)}
      // onFocus={() => {
      //   if (textInputRef?.current != document.activeElement) {
      //     textInputRef?.current?.focus();
      //   }
      // }}
      anchorEl={anchorEl}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
      PaperProps={{
        style: {
          backgroundColor: 'transparent',
          border: '0px',
          boxShadow: 'none'
        }
      }}
      onKeyDown={onKeyDown}
      onKeyPress={onKeyPress}
    >
      <div style={{ minWidth: '400px', width, backgroundColor: 'transparent' }}
        onClick={() => handleClose(false)}
      >
        <div
          style={styles.container}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {
            !!selectedText?.trim() &&
            <div style={{
              padding: '10px',
              paddingBottom: '0px',
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              maxWidth: '100%'
            }}>
              <div style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                overflow: 'hidden',
              }}>
                <span style={{ color: 'gray', fontSize: 14, whiteSpace: 'nowrap' }}>
                  {intl.formatMessage({ id: 'task_content_from_selected' })}
                </span>
                <Tooltip title={selectedText} placement='top'>
                  <div style={{
                    backgroundColor: '#f3f3f3',
                    fontSize: 14,
                    color: '#333',
                    padding: '2px',
                    paddingLeft: '4px',
                    paddingRight: '4px',
                    borderRadius: 5,
                    marginLeft: '4px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}>{selectedText}</div>
                </Tooltip>
              </div>
              <Tooltip
                title={intl.formatMessage({ id: 'ask_ai_about_selected_tooltip' })}
                placement='top'
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    cursor: 'pointer',
                    fontSize: 14,
                    color: !aiResponse && !loading && selected_content_chooseable ? (attach_selected_content && 'dodgerblue' || !attach_selected_content && '#333') : '#bbb',
                    columnGap: 4,
                    whiteSpace: 'nowrap'
                  }}
                  onClick={() => {
                    if (aiResponse || loading || !selected_content_chooseable) return;

                    set_attach_selected_content(prevState => {
                      if (!prevState && context?.type == 'task_content_from_selected') {
                        setContext({
                          type: 'none'
                        })
                      }
                      return !prevState
                    })
                  }}
                >
                  {
                    attach_selected_content &&
                    <CheckboxChecked size={18} />
                  }
                  {
                    !attach_selected_content &&
                    <CheckboxUnchecked size={18} />
                  }
                  {intl.formatMessage({ id: 'ask_ai_about_selected' })}
                </div>
              </Tooltip>
            </div>
          }
          {
            form && !(loading || aiResponse) &&
            <div style={{
              padding: '10px',
              paddingBottom: '0px',
              display: 'flex',
              flexDirection: 'column',
            }}>
              <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', whiteSpace: 'nowrap' }}>
                <div style={{ backgroundColor: '#eee', padding: '3px', paddingLeft: '6px', paddingRight: '6px', borderRadius: '4px', marginRight: '6px' }}>
                  {form.item.label}
                </div>
                {
                  form.item.sub_items &&
                  <Selector
                    options={form.item.sub_items.map((item, index) => {
                      return item
                    })}
                    value={form.sub_item.value}
                    onChange={(value) => {
                      setForm({
                        ...form,
                        sub_item: form.item.sub_items.find(subItem => subItem.value === value)
                      })
                    }}
                  />
                }
                {
                  form.sub_item?.value === 'draft_more' &&
                  <input
                    ref={form_input_ref}
                    value={form.sub_item.label === '...' ? '' : form.sub_item.label || ''}
                    onChange={(event) => {
                      setForm({
                        ...form,
                        sub_item: { ...form.sub_item, label: event.target.value }
                      })
                    }}
                    style={{
                      padding: '4px',
                      marginLeft: '6px',
                      fontSize: '15px',
                      width: '200px',
                      border: '1px solid #ccc',
                      borderRadius: '4px',
                      outline: 'none',
                    }}
                    placeholder={intl.formatMessage({ id: 'draft_more_type' })}
                    autoFocus={true}
                  />
                }
              </div>
              {
                form.item?.subTasks?.filter(item => !item.to_arg)?.map((subTask) => {
                  return <div
                    key={subTask.label}
                    style={{ paddingTop: '8px', display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: '6px', marginBottom: '4px' }}>
                    {renderSubTaskButton(subTask)}
                  </div>
                })
              }
              {
                form.args.map((arg, index) => {
                  if (arg.disabled) {
                    return null
                  }

                  const subTask = form.item?.subTasks?.find(sub => sub.to_arg === arg.name);

                  return <div key={index} style={{ paddingTop: '8px' }}>
                    <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: '6px', marginBottom: '4px' }}>
                      <div style={{ color: '#222', fontSize: 15 }}>{arg.label}</div>
                      {
                        !arg.readOnly &&
                        <div style={{ color: '#666', fontSize: '14px' }}> {'(' + intl.formatMessage({ id: arg.required ? 'arg_required' : 'arg_optional' }) + ')'} </div>
                      }
                      {
                        subTask && renderSubTaskButton(subTask)
                      }
                    </div>
                    {
                      //TODO: add Markdown render here
                      arg.readOnly && arg.value &&
                      <div style={{
                        borderRadius: '3px',
                        color: '#333', outline: 'none',
                        backgroundColor: '#f8f8f8',
                        paddingLeft: '8px', paddingRight: '8px', paddingTop: '4px', paddingBottom: '4px',
                        fontSize: 14,
                        maxHeight: '200px',
                        overflowX: 'hidden',
                        overflowY: 'auto'
                      }}>
                        <MarkdownRenderer content={arg.value} />
                      </div>
                    }
                    {
                      ['text', 'textarea'].includes(arg.type) && !arg.readOnly &&
                      <textarea
                        ref={index === 0 ? form_input_ref : null}
                        rows={arg.name === 'other_reqs' && 3 || 5}
                        autoComplete='off'
                        placeholder={arg.hint}
                        style={{
                          width: '-webkit-fill-available',
                          border: 'solid 1px lightgray', borderRadius: '3px',
                          color: '#333', outline: 'none',
                          paddingLeft: '4px', paddingRight: '4px',
                          fontSize: 15
                        }}
                        value={arg.value || ''}
                        onChange={(event) => {
                          let args = [...form.args];
                          args[index] = {
                            ...args[index],
                            value: event.target.value
                          }

                          setForm({
                            ...form,
                            args
                          })
                        }}
                      />
                    }
                    {
                      ['textline', 'input'].includes(arg.type) && !arg.readOnly &&
                      <input
                        ref={index === 0 ? form_input_ref : null}
                        autoComplete='off'
                        placeholder={arg.hint}
                        style={{
                          width: '-webkit-fill-available', border: 'solid 1px lightgray', borderRadius: '3px',
                          color: '#333', outline: 'none',
                          paddingLeft: '4px', paddingRight: '4px',
                          fontSize: 15, paddingTop: '4px', paddingBottom: '4px'
                        }}
                        value={arg.value || ''}
                        onChange={(event) => {
                          let args = [...form.args];
                          args[index] = {
                            ...args[index],
                            value: event.target.value
                          }

                          setForm({
                            ...form,
                            args
                          })
                        }}
                      />
                    }
                    {
                      arg.type === 'select' && !arg.readOnly &&
                      <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', whiteSpace: 'nowrap' }}>
                        <Selector
                          options={arg.options.map((option, index) => {
                            return option
                          })}
                          value={arg.value}
                          onChange={(value) => {
                            let args = [...form.args];
                            args[index] = {
                              ...args[index],
                              value
                            }

                            setForm({
                              ...form,
                              args
                            });
                          }}
                        />
                        {
                          (!arg.value || !arg.options.find(option => option.value === arg.value)) &&
                          <input
                            autoComplete='off'
                            placeholder={intl.formatMessage({ id: 'user_input_option' })}
                            style={{
                              width: '-webkit-fill-available', border: 'solid 1px lightgray', borderRadius: '3px',
                              color: '#333', outline: 'none',
                              paddingLeft: '4px', paddingRight: '4px',
                              marginLeft: '8px',
                              fontSize: 15, paddingTop: '4px', paddingBottom: '4px'
                            }}
                            value={arg.value || ''}
                            onChange={(event) => {
                              let args = [...form.args];
                              args[index] = {
                                ...args[index],
                                value: event.target.value
                              }

                              setForm({
                                ...form,
                                args
                              })
                            }}
                          />
                        }
                      </div>
                    }
                  </div>
                })
              }
            </div>
          }

          {
            (aiResponse || errMsg) &&
            <div
              style={{ padding: '8px', paddingBottom: '0px', fontSize: 14, maxHeight: '220px', overflowY: 'auto' }}
              ref={contentContainerRef}
            >
              {
                !!aiResponse?.content &&
                <MarkdownRenderer content={aiResponse.content} />
              }

              {
                aiResponse?.groundingMetadata &&
                <SearchGroundings data={aiResponse.groundingMetadata} />
              }

              {
                (errMsg || aiResponse?.err) &&
                <div style={{ color: 'red' }}>
                  {
                    errMsg || aiResponse?.err
                  }
                </div>
              }
            </div>
          }

          {
            aiResponse?.err == 'exceed_msg_limit' &&
            <div style={{ display: 'flex', flexDirection: 'row', padding: '10px', alignItems: 'center' }}>
              <div style={{ paddingLeft: '16px', paddingRight: '16px', paddingTop: '4px', paddingBottom: '4px', cursor: 'pointer', color: 'white', fontWeight: 'bold', backgroundColor: 'dodgerblue', borderRadius: '6px' }} onClick={() => gotoMarket()}>{intl.formatMessage({ id: 'goto_vip_buy_page' })}</div>
              <span style={{ marginLeft: '8px', marginRight: '8px', fontSize: '14px', color: 'gray' }}>{intl.formatMessage({ id: 'or_invite_friend_rewards' })}</span>
              <div style={{ paddingLeft: '16px', paddingRight: '16px', paddingTop: '4px', paddingBottom: '4px', cursor: 'pointer', color: 'white', fontWeight: 'bold', backgroundColor: 'limegreen', borderRadius: '6px' }} onClick={() => inviteFriends()}>{intl.formatMessage({ id: 'invite_friends' })}</div>
            </div>
          }

          <div
            className='fill-available'
            style={{
              // padding: '6px',
              paddingLeft: '10px',
              paddingRight: '10px',
              display: 'flex',
              flexDirection: extendInput ? 'column' : 'row',
              alignItems: extendInput ? undefined : 'center',
              justifyContent: 'flex-end',
              paddingBottom: 8,
              paddingTop: 8
            }}>
            <div
              className='fill-available'
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',

              }}
            >
              {
                !extendInput && ai_magic_icon_and_status
              }
              {
                !loading && (!form || aiResponse) &&
                <div
                  className='fill-available'
                  style={{
                    position: 'relative',
                    borderRadius: 4,
                    padding: 6,
                    columnGap: 6,
                    paddingTop: 3,
                    paddingBottom: 2,
                    paddingRight: 0,
                    border: '1px solid #d8d8d8',
                    marginRight: extendInput ? 0 : 6,
                  }}
                  onKeyDown={(event) => {
                    if (event.key === 'Backspace') {
                      if (searchText.length === 0) {
                        event.stopPropagation();
                        event.preventDefault();
                        if (!aiResponse) {
                          handleClose();
                        }
                      }
                    }
                  }}
                  onMouseDown={(event) => {
                    event.stopPropagation();
                  }}
                >
                  <ContentEditable
                    innerRef={textInputRef}
                    style={{
                      // width: 'calc(100% - 32px)',
                      // border: '1px solid #eee',
                      border: '0px',
                      outline: 'none',
                      fontSize: 15,
                      alignContent: 'flex-end',
                      cursor: 'text',
                      maxHeight: 400,
                      overflowY: 'auto'
                    }}
                    html={userInput || ''}
                    onChange={(event) => {
                      setUserInput(event.target.value);
                    }}

                    onPaste={(e) => {
                      e.preventDefault();

                      const text = e.clipboardData.getData('text/plain');
                      document.execCommand('insertText', false, text);
                    }}
                    onCompositionStart={() => setIsInputZh(true)}
                    onCompositionEnd={() => setIsInputZh(false)}
                  />

                  <span
                    className='fill-available'
                    style={{
                      position: 'absolute',
                      left: 7,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      fontSize: 14,
                      color: '#ccc',
                      pointerEvents: 'none',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {!userInput && intl.formatMessage({ id: aiResponse ? 'askAI_next' : 'askAI' })}
                  </span>
                </div>
              }
            </div>
            <div
              // className={extendInput ? 'fill-available' : undefined}
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: extendInput ? 'space-between' : 'flex-end',
                rowGap: 4,
                marginTop: extendInput ? 8 : undefined,
              }}
            >
              {/* {
                showContextFormInAskAI &&
                contextForm
              } */}
              {
                !!extendInput && ai_magic_icon_and_status
              }
              <div style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}>
                {
                  (form || showContextFormInAskAI) && !loading && !aiResponse &&
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    width: '100%'
                  }}>
                    {
                      contextForm
                    }
                  </div>
                }
                <div style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center'
                }}>
                  {
                    <ModelSelector
                      dropdownIconSize={undefined}
                      value={temp_llm_model}
                      onSelect={(value) => {
                        set_temp_llm_model(value);
                      }}
                      inputStyle={{
                        maxWidth: 100
                      }}
                      show_search_web_button={true}
                    />
                  }
                  {
                    !loading && (!!searchText?.trim() || form) &&
                    <div
                      className='hover_opacity'
                      style={{
                        paddingTop: '4px',
                        paddingBottom: '4px',
                        paddingLeft: '10px',
                        paddingRight: '10px',
                        marginLeft: '8px',
                        color: 'white', cursor: 'pointer',
                        backgroundColor: subTasking ? '#ddd' : 'dodgerblue',
                        borderRadius: '14px',
                        whiteSpace: 'nowrap',
                        fontSize: '14px'
                      }} onClick={() => !subTasking && askAI()}
                    >
                      {intl.formatMessage({ id: 'sendAI' })}
                    </div>
                  }
                </div>
              </div>
            </div>
          </div>
          {
            aiResponse && <div style={{
              padding: '10px',
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              color: 'gray',
              backgroundColor: '#f5f5f5',
              fontSize: 12,
              columnGap: 4
            }}>
              <Warning size={16} />
              {intl.formatMessage({ id: 'ai_response_warning' })}
            </div>
          }
        </div>
        <div
          style={{ flexDirection: 'row' }}
        >
          <div
            style={{ ...styles.container, width: '320px', maxHeight: '300px', overflowY: 'auto' }}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            {
              !confirmMessage &&
              groupedAssistantItems?.map((item, index) => {
                return <div
                  // ref={item.order === itemTargeted ? selectedItemRef : null}
                  ref={(ref) => {
                    elmRefs.current = { ...elmRefs.current, [item.order]: ref };
                  }}
                  key={index + ''}
                >
                  {
                    item.type === 'groupName' &&
                    <div style={{ fontSize: 13, color: 'gray', padding: '6px', display: 'flex', flexDirection: 'row' }}>
                      {item.label}
                      {
                        item.type === 'user_installed' &&
                        <div style={{
                          paddingLeft: '6px', paddingRight: '6px', cursor: 'pointer',
                          display: 'flex', justifyContent: 'flex-end',
                          flex: 1,
                          whiteSpace: 'nowrap',
                          // color: 'dodgerblue'
                        }} onClick={() => gotoPrompts()}>{intl.formatMessage({ id: 'CRUD' })}</div>
                      }
                    </div>
                  }
                  {
                    item.type === 'divider' && !!index && groupedAssistantItems.filter(it => it.type != 'divider').length > 2 &&
                    <Divider style={{ marginLeft: 5, marginRight: 8 }} />
                  }
                  {
                    !['groupName', 'divider'].includes(item.type) &&
                    <div
                      key={index + ''}
                      onClick={() => itemSelected({ order: item.order })}
                      className='hoverStand'
                      style={{
                        backgroundColor: item.order === itemTargeted ? 'rgb(212,228,247)' : undefined,
                        padding: '5px', paddingLeft: '12px',
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between'
                      }}
                    >
                      <div style={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: 'block',
                        columnGap: 9,
                        display: 'flex',
                        flexDirection: 'row'
                      }}>
                        {!!item.icon && item.icon}
                        {item.label}
                      </div>
                      {item.order === itemTargeted && <KeyboardReturn size={20} style={{ cursor: 'pointer', color: 'gray' }} />}
                    </div>
                  }
                </div>
              })
            }
            {
              confirmMessage &&
              <ConfirmMessage
                content={confirmMessage.content}
                onCancel={() => setConfirmMessage(null)}
                onConfirm={confirmMessage.onConfirm}
                style={{
                  padding: 10
                }}
                optionSwitcher={confirm_option_switcher}
                optionEnter={confirm_option_enter}
              />
            }
          </div>
        </div>
        <Popover
          open={Boolean(sub_menu_visible)}
          onClose={() => close_sub_menu()}
          onClick={(e) => {
            e.stopPropagation();
          }}
          anchorEl={sub_menu_anchor}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: 'transparent'
          }}
        >
          <div
            style={{ ...styles.container, width: 200, margin: 0 }}
          >
            {
              sub_menu_items?.map((item, index) => {
                return <div
                  ref={(ref) => {
                    sub_menu_item_refs.current = { ...sub_menu_item_refs.current, [index]: ref };
                  }}
                  key={index + ''}
                >
                  <div
                    key={index + ''}
                    onClick={(e) => { sub_menu_item_clicked(item) }}
                    className='hoverStand'
                    style={{ backgroundColor: index === sub_menu_item_targeted ? 'rgb(212,228,247)' : undefined, flexDirection: 'row', justifyContent: 'space-between', padding: '5px', paddingLeft: '12px' }}
                  >
                    {item.label}
                    {index === sub_menu_item_targeted && <KeyboardReturn size={20} style={{ cursor: 'pointer', color: 'gray' }} />}
                  </div>
                </div>
              })
            }
          </div>
        </Popover>
      </div>
    </Popover >
  )
}

const styles = {
  container: {
    backgroundColor: 'white',
    boxShadow: '0px 0px 8px #bbb',
    margin: '5px',
    borderRadius: '5px'
  },
  contentSect: {
    fontSize: 13,
    color: 'gray'
  }
}

export default AIModal;
