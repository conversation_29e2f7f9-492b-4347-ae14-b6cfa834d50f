import * as React from 'react';
import { IMPORT_DOC_DIALOG, OPERATION_FAILED } from 'src/constants/actionTypes';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { DialogContent, Dialog, Button, DialogTitle, Tooltip, DialogActions, FormGroup, FormControlLabel, Checkbox } from '@mui/material';
import { useIntl } from 'react-intl';
import { border } from '@mui/system';
import { useFilePicker } from 'use-file-picker';
import { uploadFiles } from 'src/actions/ticketAction';
import { linkToPage } from 'src/utils/PageLinkMaker';
import { Selector } from '../common/Selector';
import { DB_PROPERTY_TYPES } from 'src/constants/constants';
import { cloneDeep } from 'lodash';
import LoadingScreen from '../LoadingScreen';

const ImportFileTypes = [{
//     label: 'Word',
//     value: 'word',
//     accept: ['.doc', '.docx']
// }, {
    label: 'Markdown/Text',
    value: 'md',
    accept: ['.txt', '.md']
}, {
    label: 'Html',
    value: 'html',
    accept: ['.html', '.htm']
}, {
    label: 'CSV',
    value: 'csv',
    accept: ['.csv']
}]

const ImportModal = ({ }) => {
    const dialogState = useSelector(state => state.uiState.importDocDialog) || { visible: false };
    const dispatch = useDispatch();
    const intl = useIntl();
    const history = useHistory();

    const [fileType, setFileType] = React.useState();
    const [readyToUpload, setReadyToUpload] = React.useState();
    const [settingsPage, showSettingsPage] = React.useState();
    const [uploading, setUploading] = React.useState();
    const initSettings = {
        hasHeader: true,
        properties: {
            p0: {
                type: 'Title'
            }
        }
    }
    const [settings, setSettings] = React.useState(initSettings);
    const [firstLine, setFirstLine] = React.useState();

    const handleClose = () => {
        setSettings(initSettings);
        showSettingsPage(false);
        setReadyToUpload(false);
        dispatch({ type: IMPORT_DOC_DIALOG, value: { visible: false } });
    }

    const [openFileSelector, { filesContent, loading, errors, clear }] = useFilePicker({
        readAs: ['word'].includes(fileType) ? 'BinaryString' : 'Text',
        // readAs: 'DataURL',
        accept: ImportFileTypes.find(ft => ft.value === fileType)?.accept,
        multiple: false,
        // limitFilesConfig: {max: 1 },
        // // minFileSize: 0.1, // in megabytes
        maxFileSize: 10,
        // imageSizeRestrictions: {
        //   maxHeight: 800, // in pixels
        //   maxWidth: 1600,
        //   minHeight: 600,
        //   minWidth: 768,
        // },
    });

    const [openFileCount, setOpenFileCount] = React.useState(0);


    React.useEffect(() => {
        if (openFileCount) {
            openFileSelector();
        }
    }, [openFileCount]);


    const pickFile = (type) => {
        setFileType(type);

        clear();
        setOpenFileCount(openFileCount + 1);
    }

    React.useEffect(() => {
        if (filesContent && filesContent.length && filesContent[0]) {
            // console.log('file selected..............', filesContent[0])
            if (fileType === 'csv') {
                const lines = filesContent[0].content?.trim().split('\n');
                let noneEmptyLine;
                while (!noneEmptyLine) {
                    noneEmptyLine = lines.shift().replace(' ', '').trim();
                }

                if (noneEmptyLine) {
                    setFirstLine(noneEmptyLine);
                    showSettingsPage(true);
                } else {
                    clear();
                }
            } else {
                setReadyToUpload(true);
            }
        }
    }, [filesContent]);

    React.useEffect(() => {
        if (!readyToUpload) {
            return;
        }

        if (filesContent && filesContent.length && filesContent[0]) {
            let data = { files: [filesContent[0]], fileType, hid: dialogState.hid, space: dialogState.space, settings };

            setUploading(true);
            dispatch(uploadFiles({
                data,
                enctype: 'multipart'
            }, (doc) => {
                setUploading(false);

                if (!doc) {
                    return dispatch({ type: OPERATION_FAILED, message: 'Failed to import page' });
                }

                clear();
                history.push(linkToPage(doc, { space: dialogState.space }));

                handleClose();
            }, () => {
                setUploading(false);

                clear();
            }, 'editor'));
        }

        setReadyToUpload(false);
    }, [readyToUpload]);

    const DB_TYPES = React.useMemo(() => {
        return DB_PROPERTY_TYPES.filter(t => !t.advancedType && ['Title', 'Text', 'Number', 'Date', 'Select', 'MultiSelect'].includes(t.value));
    }, [])

    return (
        <Dialog
            open={!!dialogState && dialogState.visible}
            onClose={handleClose}
            scroll='paper'
            aria-labelledby="scroll-dialog-title"
            aria-describedby="scroll-dialog-description"
            maxWidth='sm'
        >
            <div style={{ width: '100%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
                <div style={{ padding: '8px', fontWeight: 500, color: 'gray' }}>{intl.formatMessage({ id: 'import' })}</div>
            </div>
            <DialogContent dividers={true} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'space-evenly', minHeight: 240, minWidth: 360, padding: 12 }}>
                {
                    !uploading &&
                    !settingsPage && ImportFileTypes.map((fileType, i) => {
                        return <div key={i + ''} className='hoverStand' style={styles.button} onClick={() => pickFile(fileType.value)}>
                            {fileType.label}
                        </div>
                    })
                }

                {
                    !uploading &&
                    settingsPage &&
                    <div>
                        <div style={{ marginBottom: 5 }}>
                            <span style={{ fontWeight: 'bold' }}>
                                {intl.formatMessage({ id: 'csv_first_line' })}
                            </span>
                        </div>
                        <div style={{ padding: 4, backgroundColor: '#eee', marginBottom: 5 }}>
                            {firstLine.replaceAll(',', ', ')}
                        </div>

                        <div style={{ marginBottom: 5, marginTop: 5 }}>
                            <span style={{ fontWeight: 'bold' }}>
                                {intl.formatMessage({ id: 'import_settings' })}
                            </span>
                        </div>

                        <FormGroup>
                            {
                                firstLine.split(',').map((col, index) => {
                                    return <div key={index + ''} style={{ display: 'flex', flexDirection: 'row', columnGap: 6, padding: 2 }}>
                                        {intl.formatMessage({ id: 'csv_column' }, { num: index + 1 })}
                                        <Selector
                                            value={settings.properties['p' + index]?.type || 'Text'}
                                            options={
                                                DB_TYPES
                                            }
                                            onChange={
                                                (value) => {
                                                    let ps = cloneDeep(settings.properties);
                                                    if (value === 'Title') {
                                                        Object.keys(ps).forEach(key => {
                                                            if (ps[key].type === 'Title') {
                                                                ps[key].type = 'Text';
                                                            }
                                                        })
                                                    }

                                                    ps['p' + index] = {
                                                        type: value
                                                    };

                                                    setSettings({
                                                        ...settings,
                                                        properties: ps
                                                    })
                                                }
                                            }
                                        />
                                    </div>
                                })
                            }
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={settings.hasHeader}
                                        onChange={(event) => {
                                            setSettings({ ...settings, hasHeader: event.target.checked })
                                        }}
                                    />
                                }
                                label={intl.formatMessage({ id: 'csv_header_confirm' })}
                            />
                        </FormGroup>
                    </div>
                }

                {
                    uploading && <LoadingScreen />
                }

            </DialogContent>

            <DialogActions>
                <Button
                    onClick={handleClose}
                    disabled={loading}
                >
                    {
                        intl.formatMessage({ id: 'cancel' })
                    }
                </Button>
                {
                    settingsPage &&
                    <Button
                        onClick={() => {
                            setReadyToUpload(true);
                        }}
                        disabled={loading}
                    >
                        {
                            intl.formatMessage({ id: 'confirm' })
                        }
                    </Button>
                }
            </DialogActions>
        </Dialog>
    );
}

const styles = {
    button: {
        display: 'flex',
        width: '260px',
        padding: '10px',
        margin: '4px',
        flexDirection: 'row',
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        color: 'dodgerblue',
        border: 'solid 1px dodgerblue',
        borderRadius: '6px',
        fontWeight: 'bold',
        fontSize: 18
    }
}

export default ImportModal;
