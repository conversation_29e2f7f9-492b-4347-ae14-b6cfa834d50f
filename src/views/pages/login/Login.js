import React, { useState, useEffect, useLayoutEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useHistory, useLocation } from 'react-router-dom'
import {
  <PERSON>ton,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  IconButton
} from '@mui/material';
import { Visibility } from '@styled-icons/material/Visibility';
import { VisibilityOff } from '@styled-icons/material/VisibilityOff';

import * as TicketAction from '../../../actions/ticketAction';
import * as Validator from '../../../utils/validator';
import { get_server_host } from '../../../utils/serverAPIUtil';
import SpinnerAndToast from 'src/components/SpinnerAndToast';
import { useIntl } from 'react-intl';
import { getLocale } from 'src/utils/Intl';
import { Refresh } from '@styled-icons/material-outlined/Refresh'
import { isCNDomain } from 'src/utils/constants';

import { Email } from '@styled-icons/material-outlined/Email';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { Password } from '@styled-icons/material/Password'
import LoadingScreen from '@/components/LoadingScreen';
import { useMediaQuery } from 'react-responsive';
import { GoogleLogin } from './GoogleLogin';
import ReactGA from "react-ga4";
import { MOBILE_MEDIA_QUERY } from '../../../utils/constants';

const Login = () => {
  const loginUser = useSelector(state => state.loginIn.user);
  const [account, setAccount] = useState('');
  const [accountValid, setAccountValid] = useState(true);
  const [isAccountPhone, setIsAccountPhone] = useState(false);
  const [nickname, setNickname] = useState('');
  const [nicknameValid, setNicknameValid] = useState(true);
  const [password, setPassword] = useState('');
  const [passwordValid, setPasswordValid] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [captcha, setCaptcha] = useState('');
  const [captchaRefresher, setCaptchaRefresher] = useState('');
  const [serverHost, setServerHost] = useState();
  const [captchaValid, setCaptchaValid] = useState(false);
  const [vcode, setVcode] = useState('');
  const [vcodeValid, setVcodeValid] = useState(true);
  const [vCodeRequested, setVCodeRequested] = useState(false);
  const [invitation, setInvitation] = useState('');
  const vcode_ref = useRef();

  const [submitting, setSubmitting] = useState(false);
  const [googleLoging, setGoogleLoging] = useState(false);
  const [loginMethod, setLoginMethod] = useState();
  const dispatch = useDispatch();
  const history = useHistory();
  const intl = useIntl();

  const currentLocation = useLocation();
  const params = new Proxy(new URLSearchParams(currentLocation.search), {
    get: (searchParams, prop) => searchParams.get(prop),
  });

  const isMobile = useMediaQuery(MOBILE_MEDIA_QUERY)


  const doResetPswd = (type) => {
    // this.setState({ commitId: 'logSign' });

    if (accountValid && vcodeValid && passwordValid && captchaValid) {
      setSubmitting(true);

      dispatch(TicketAction.resetPassword({ username: account, vcode, password, type }, passwordUpdated, path));
      refreshCaptcha();
    }
  }

  const passwordUpdated = () => {
    setSubmitting(false);
    setPassword('');
    history.push('/login')
  }

  const doRegister = () => {
    // this.setState({ commitId: 'logSign' });

    if (accountValid && vcodeValid && nicknameValid && passwordValid && (captchaValid || (invitation && invitation.invitedUserAccount == account))) {
      setSubmitting(true);

      dispatch(TicketAction.register({ username: account, vcode, nickname, password, invitationId: params.invitationId, inviteCode: inviteCode, aid, locale: getLocale() }, logon, path));
      refreshCaptcha();
    }
  }

  const doLogin = () => {
    // this.setState({ commitId: 'logSign' });

    if (accountValid && (path === '/login' && password && passwordValid || path === '/vcodelogin' && vcode && vcodeValid)) {
      let params = {
        username: account,
        locale: getLocale(),
        inviteCode,
        aid
      }

      if (path == '/login') {
        params.password = password;
      } else if (path == '/vcodelogin') {
        params.vcode = vcode;
        params.mode = 'temp_token';
      }
      dispatch(TicketAction.login(params, logon, null, 'login'));
    }
  }

  const logon = (user) => {
    if (user?._id) {
      if (user.activated) {
        if (params?.source == 'extension') {
          // window.location.href = `https://${getTopLevelDomain()}/welcome_extension.html`;
          window.location.href = '/#/invitation-event?greetings=logon&source=' + params?.source;
          return
        } else if (params?.source == 'flow' || isMobile) {
          window.location.href = '/#/aiflow?source=' + params?.source;
          return
        }

        if (currentLocation.pathname.includes('/login') || currentLocation.pathname.includes('/register')) {
          currentLocation.pathname = '/'
        }
        // else {
        //   currentLocation.pathname = '/login'
        // }
        history.push(currentLocation);
      } else {
        history.push("/activate")
      }
    }

    setSubmitting(false);
    setGoogleLoging(false);
  }

  useEffect(() => {
    if (loginUser) logon(loginUser);
  }, [loginUser])

  useEffect(() => {
    dispatch(TicketAction.getUserInfo());
  }, [])

  useLayoutEffect(() => {
    get_server_host().then((url) => setServerHost(url))
  }, [])

  useEffect(() => {
    // ReactGA.initialize(GOOGLE_ANALYTICS_TRACKID);
    ReactGA.send({ hitType: "pageview", page: "/login", title: "Login" });
  }, [])

  let { pathname, search } = useLocation();

  const invited_code = useSelector(state => state.uiState.invited_code)
  const [inviteCode, setInviteCode] = useState(params.inviteCode || invited_code);
  const [aid, setAid] = useState(params.aid);

  useEffect(() => {
    if (params.invitationId) {
      dispatch(TicketAction.getInvitation({ invitationId: params.invitationId }, invitation => {
        setInvitation(invitation);
        setAccount(invitation.invitedUserAccount);
        if (!invitation.userActivated) {
          history.push({ pathname: '/register', search });
        }
      }, 'register'));
    }
  }, [params.invitationId]);

  useEffect(() => {
    if (params.g_login_token) {
      setGoogleLoging(true);
      dispatch(TicketAction.oauth_sign_in_credential({ credential: params.g_login_token, inviteCode, aid }, logon))

      return;
    }
  }, [params.g_login_token])

  const pages = [
    {
      path: '/login',
      action: 'login',
      title: !loginMethod ? intl.formatMessage({ id: 'sign_in' }) : intl.formatMessage({ id: 'sign_in_with' }, { method: intl.formatMessage({ id: 'password_account' }) }),
      btnLabel: intl.formatMessage({ id: 'login' }),
      btnAction: doLogin,
      formInfoText: intl.formatMessage({ id: "login_signin_form_info" })
    },
    {
      path: '/vcodelogin',
      action: 'login',
      title: intl.formatMessage({ id: 'sign_in_with_email_vcode' }),
      btnLabel: intl.formatMessage({ id: 'login' }),
      btnAction: doLogin,
      formInfoText: intl.formatMessage({ id: "login_signin_vcode_form_info" })
    },
    {
      path: '/forget',
      action: 'forget',
      title: intl.formatMessage({ id: 'resetpswd' }),
      btnLabel: intl.formatMessage({ id: 'resetpswd' }),
      btnAction: () => doResetPswd('forget'),
      formInfoText: intl.formatMessage({ id: "login_resetpswd_form_info" })
    },
    {
      path: '/register',
      action: 'register',
      title: intl.formatMessage({ id: 'signup' }),
      btnLabel: intl.formatMessage({ id: 'signup' }),
      btnAction: doRegister,
      formInfoText: intl.formatMessage({ id: "login_signup_form_info" })
    }
  ];
  const { title, btnLabel, formInfoText, btnAction, path, action } = pages.find(ele => ele.path === pathname) || pages[0];

  const AccountInput = {
    Phone: {
      placeholder: 'phone',
      validator: Validator.validatePhone
    },
    Email: {
      placeholder: 'email',
      validator: Validator.validateEmail
    },
    PhoneOrMail: {
      placeholder: 'phone_or_email',
      validator: (account) => Validator.validatePhone(account) || Validator.validateEmail(account)
    }
  }
  const AccountMode = path === '/vcodelogin' || !isCNDomain() ? 'Email' : 'PhoneOrMail';

  const onAccountBlur = (e) => {
    let value = e.target.value ? e.target.value.trim() : '';
    setAccount(value);
    setAccountValid(AccountInput[AccountMode].validator(value));
    setIsAccountPhone(AccountInput[AccountMode].validator(value) && Validator.validatePhone(value));
  }

  const [vCoderTimer, setVCoderTimer] = useState(0);
  const vCoderTimerCounting = vCoderTimer < 120 && vCoderTimer > 0;
  const sendVerificationCode = () => {
    // this.setState({ commitId: 'sendVCode' });

    if (accountValid && captchaValid) {
      setSubmitting(true);
      dispatch(TicketAction.sendVerificationCode({ username: account, type: btnLabel.toLowerCase(), type: action }, vCodeSent, path));
    }
  }

  let clockCall;
  const vCodeSent = () => {
    setSubmitting(false);
    setVCodeRequested(true);
    vcode_ref.current.focus();

    setVCoderTimer(120);
    clockCall = setInterval(() => {
      decrementClock();
    }, 1000);

  }

  const decrementClock = () => {
    setVCoderTimer(vCoderTimer => {
      if (vCoderTimer === 0) {
        clearInterval(clockCall);
      }

      return vCoderTimer - 1
    });
  };

  // showAccountInactiveModal = (isVisible) => {
  //     this.setState({ isAccountInactiveModalVisible: isVisible });
  // }

  // resendActivationEmail = () => {
  //     const { dispatch, navigation } = this.props;
  //     const { account } = this.state;

  //     this.setState({
  //         commitId: 'sendActivationEmail'
  //     })
  //     dispatch(TicketAction.resendActivationEmail({ email: account }, () => this.setState({ commitId: null, activationEmailResent: true }), navigation.state.key));
  // };

  const refreshCaptcha = () => {
    setCaptchaRefresher(Math.random());
  }

  const onCaptchaChange = (captcha) => {
    setCaptcha(captcha);

    if (!captcha || !captcha.trim() || captcha.trim().length != 4) {
      return setCaptchaValid(false);
    }

    // this.setState({ commitId: 'verifyingCaptcha' })
    dispatch(TicketAction.verifyCaptcha({ captcha: captcha.trim() }, ({ verified }) => setCaptchaValid(verified), path));
  }

  const captchaUri = `${serverHost}users/captcha?${captchaRefresher}`;

  return (
    <div className='full-height' style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', background: '#eee', padding: 10 }}>
      {
        !googleLoging &&
        <Card className='fill-available' style={{ display: 'flex', maxWidth: '540px', height: 'fit-content', flexDirection: 'row', alignItems: 'center', backgroundColor: '#321fdb', alignSelf: 'center', }}>
          <Card className='fill-available'>
            <CardContent style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
              <h2 style={{ marginBottom: 24 }}>{title}</h2>
              {
                !loginMethod &&
                <div style={{ flex: 1, display: 'flex', flexDirection: 'column', rowGap: 20, paddingTop: 15 }}>
                  {
                    !(isCNDomain() || params.g_login_token) &&
                    <GoogleLogin logon={logon} setGoogleLoging={setGoogleLoging} showButton={true} inviteCode={inviteCode} aid={aid}/>
                  }

                  <button
                    type="button"
                    className='loginMethodButton'
                    onClick={() => {
                      setLoginMethod('verification_code');
                      history.push({ pathname: '/vcodelogin', search });
                    }}
                  >
                    <Email size={24} style={{ padding: 6 }} /> {intl.formatMessage({ id: 'sign_in_with_email_vcode' })}
                  </button>

                  <button
                    type="button"
                    className='loginMethodButton'
                    onClick={() => {
                      setLoginMethod('password');
                    }}
                  >
                    <Password size={24} style={{ padding: 6 }} /> {intl.formatMessage({ id: 'sign_in_with_password' })}
                  </button>
                  <div style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'flex-end', fontSize: 14 }}>
                    <a href='https://funblocks.net/privacypolicy_en.html' target='_blank'>Privacy Policy</a>
                  </div>
                </div>
              }

              {['password', 'verification_code'].includes(loginMethod) && <>

                <p className="text-medium-emphasis" style={{ marginBottom: 12 }}>{formInfoText}</p>

                <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-around', flex: 1 }}>
                  <TextField
                    value={account}
                    label={intl.formatMessage({ id: AccountInput[AccountMode].placeholder })}
                    style={{
                      margin: "10px 0px",
                    }}
                    error={!accountValid}
                    autoComplete="username"
                    required={true}
                    onChange={(e) => {
                      setAccount(e.target.value ? e.target.value.trim() : '');
                    }}
                    onBlur={onAccountBlur}
                  />

                  {
                    path === '/register' &&
                    <TextField
                      value={nickname}
                      label={intl.formatMessage({ id: 'nickname' })}
                      style={{
                        margin: "10px 0px",
                      }}
                      error={!nicknameValid}
                      autoComplete="off"
                      required={true}
                      onChange={(e) => {
                        setNickname(e.target.value);
                      }}
                      onBlur={(e) => setNicknameValid(e.target.value && e.target.value.length >= 4 && e.target.value.length <= 20)}
                    />
                  }

                  {
                    path !== '/login' && (path !== '/register' || !invitation || invitation.invitedUserAccount != account) &&

                    <TextField
                      value={captcha}
                      label={intl.formatMessage({ id: 'captcha' })}
                      style={{
                        margin: "10px 0px",
                      }}
                      required={true}
                      autoComplete='off'
                      placeholder={intl.formatMessage({ id: 'captcha' })}
                      helperText={captchaValid ? '' : intl.formatMessage({ id: 'vcode_err' })}
                      error={!captchaValid}
                      InputProps={{
                        startAdornment: <InputAdornment
                          position="start"
                        >
                          <div
                            style={{ cursor: 'pointer', display: 'flex', flexDirection: 'row', alignItems: 'center' }}
                            title="click to refresh"
                            onClick={refreshCaptcha}
                            onMouseDown={(event) => event.preventDefault()}
                            edge="end"
                          >
                            <img
                              src={captchaUri}
                            />
                            <Refresh size={28} />
                          </div>

                        </InputAdornment>
                      }}
                      onChange={(e) => {
                        onCaptchaChange(e.target.value);
                      }}
                    />
                  }

                  {
                    path !== '/login' &&
                    !(invitation && invitation.invitedUserAccount == account) &&
                    !(path === '/register' && !isAccountPhone) &&
                    <TextField
                      value={vcode}
                      label={intl.formatMessage({ id: 'verification_code' })}
                      style={{
                        margin: "10px 0px",
                      }}
                      disabled={!vCodeRequested}
                      ref={vcode_ref}
                      required={true}
                      InputProps={{
                        endAdornment: <InputAdornment
                          position="end"
                        >
                          <Button
                            onClick={sendVerificationCode}
                            onMouseDown={(event) => event.preventDefault()}
                            disabled={vCoderTimerCounting || !captchaValid || submitting}

                            edge="start"
                            variant="contained"
                          >
                            {vCoderTimerCounting ? vCoderTimer + 's' : intl.formatMessage({ id: 'getvcode' })}
                          </Button>

                        </InputAdornment>
                      }}
                      onChange={(e) => {
                        setVcode(e.target.value);
                      }}
                      onBlur={(e) => setVcodeValid(e.target.value && e.target.value.length >= 4 && e.target.value.length <= 6)}
                      error={!vcodeValid}
                    />
                  }
                  {
                    path != '/vcodelogin' &&
                    <TextField
                      type={showPassword ? 'text' : 'password'}
                      label={intl.formatMessage({ id: 'password' })}
                      style={{
                        margin: "10px 0px",
                      }}
                      value={password}
                      autoComplete="current-password"
                      required={true}
                      onChange={(e) => setPassword(e.target.value)}
                      onBlur={(e) => setPasswordValid(e.target.value && e.target.value.length >= 6 && e.target.value.length <= 20)}
                      error={!passwordValid}
                      InputProps={{
                        endAdornment: <InputAdornment
                          style={{ paddingRight: '6px' }}
                          position="end"
                        >
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={() => setShowPassword(!showPassword)}
                            onMouseDown={(event) => event.preventDefault()}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff style={{ width: '24px', height: '24px' }} /> : <Visibility style={{ width: '24px', height: '24px' }} />}
                          </IconButton>
                        </InputAdornment>
                      }}
                    />
                  }
                  {
                    path == '/register' && !!inviteCode &&
                    <TextField
                      value={inviteCode}
                      label={intl.formatMessage({ id: 'invite_code' })}
                      style={{
                        margin: "10px 0px",
                      }}
                      disabled={!!params.inviteCode}
                      required={false}
                      onChange={(e) => {
                        setInviteCode(e.target.value);
                      }}
                    />
                  }
                  <div
                    style={{ display: 'flex', flexDirection: isMobile ? 'column' : 'row', rowGap: 5, paddingTop: 10 }}
                  >
                    <>
                      <Button variant="contained"
                        onClick={() => {
                          btnAction();
                        }}
                        disabled={submitting}
                      >
                        {btnLabel}
                      </Button>
                      <Button variant="text"
                        sx={{ textTransform: 'none' }}
                        onClick={() => {
                          setLoginMethod(null);
                          history.push({ pathname: '/login', search });
                        }}
                        disabled={submitting}
                      >
                        {intl.formatMessage({ id: 'choose_login_method' })}
                      </Button>
                    </>
                    {
                      path === '/login' &&
                      <>
                        <Button variant="text" onClick={() => history.push({ pathname: '/forget', search })} sx={{ textTransform: 'none' }}>
                          {intl.formatMessage({ id: 'forgotpswd' })}
                        </Button>
                        <Button variant="text" onClick={() => {
                          let newSearchParams = new URLSearchParams(search);
                          if (invitation) {
                            newSearchParams.set('invitationId', invitation._id);
                          }

                          history.push({ pathname: '/register', search: newSearchParams.toString() });
                        }} sx={{ textTransform: 'none' }}>
                          {intl.formatMessage({ id: 'not_registered' })}
                        </Button>
                      </>
                    }
                    {
                      path === '/register' &&
                      <Button variant="text" onClick={() => history.push({ pathname: '/login', search })} sx={{ textTransform: 'none' }}>
                        {intl.formatMessage({ id: 'hadaccount' })}
                      </Button>
                    }
                    {
                      path === '/forget' &&
                      <Button variant="text" onClick={() => history.push({ pathname: '/login', search })} sx={{ textTransform: 'none' }}>
                        {intl.formatMessage({ id: 'login' })}
                      </Button>
                    }
                  </div>
                </div>
              </>
              }
            </CardContent>
          </Card>
        </Card>
      }

      {
        googleLoging &&
        <LoadingScreen style={{ position: 'absolute' }} />
      }
      <SpinnerAndToast
        statusCallbacker={setSubmitting}
      />
    </div >
  )
}

const styles = {
  login_method_button: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    width: '-webkit-fill-available',
    height: '48',
    columnGap: 6,
    color: '#333',
    fontSize: 16,
    cursor: 'pointer',
    border: '1px solid #aaa'
  }
}

export default Login